/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const Board         = scripting.addModule("/ti/driverlib/Board");
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const GPIO2         = GPIO.addInstance();
const GPIO3         = GPIO.addInstance();
const GPIO4         = GPIO.addInstance();
const GPIO5         = GPIO.addInstance();
const I2C           = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1          = I2C.addInstance();
const MATHACL       = scripting.addModule("/ti/driverlib/MATHACL");
const PWM           = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1          = PWM.addInstance();
const PWM2          = PWM.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK       = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER         = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1        = TIMER.addInstance();
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const pinFunction4     = system.clockTree["HFXT"];
pinFunction4.inputFreq = 40;
pinFunction4.enable    = true;

Board.peripheral.swclkPin.$assign = "PA20";

GPIO1.$name                          = "LED";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name        = "LED1";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "1";
GPIO1.associatedPins[0].ioStructure  = "SD";
GPIO1.associatedPins[0].initialValue = "SET";
GPIO1.associatedPins[1].ioStructure  = "SD";
GPIO1.associatedPins[1].assignedPin  = "23";
GPIO1.associatedPins[1].$name        = "LED2";
GPIO1.associatedPins[1].assignedPort = "PORTA";
GPIO1.associatedPins[1].initialValue = "SET";
GPIO1.associatedPins[2].ioStructure  = "SD";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].$name        = "LED3";
GPIO1.associatedPins[2].assignedPin  = "14";
GPIO1.associatedPins[2].initialValue = "SET";
GPIO1.associatedPins[3].$name        = "LED4";
GPIO1.associatedPins[3].ioStructure  = "SD";
GPIO1.associatedPins[3].assignedPort = "PORTA";
GPIO1.associatedPins[3].assignedPin  = "7";
GPIO1.associatedPins[3].initialValue = "SET";

GPIO2.$name                              = "KEY";
GPIO2.associatedPins.create(3);
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].$name            = "KEY4";
GPIO2.associatedPins[0].assignedPort     = "PORTA";
GPIO2.associatedPins[0].assignedPin      = "26";
GPIO2.associatedPins[0].ioStructure      = "SD";
GPIO2.associatedPins[1].direction        = "INPUT";
GPIO2.associatedPins[1].internalResistor = "PULL_UP";
GPIO2.associatedPins[1].ioStructure      = "SD";
GPIO2.associatedPins[1].$name            = "KEY2";
GPIO2.associatedPins[1].assignedPort     = "PORTB";
GPIO2.associatedPins[1].assignedPin      = "24";
GPIO2.associatedPins[2].$name            = "KEY3";
GPIO2.associatedPins[2].direction        = "INPUT";
GPIO2.associatedPins[2].ioStructure      = "SD";
GPIO2.associatedPins[2].assignedPort     = "PORTA";
GPIO2.associatedPins[2].assignedPin      = "27";

GPIO3.$name                               = "SPD_READER_A";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].direction         = "INPUT";
GPIO3.associatedPins[0].interruptEn       = true;
GPIO3.associatedPins[0].$name             = "FONT_LEFT_A";
GPIO3.associatedPins[0].internalResistor  = "PULL_DOWN";
GPIO3.associatedPins[0].assignedPort      = "PORTB";
GPIO3.associatedPins[0].assignedPin       = "11";
GPIO3.associatedPins[0].interruptPriority = "1";
GPIO3.associatedPins[0].polarity          = "RISE";
GPIO3.associatedPins[0].pin.$assign       = "PB11";
GPIO3.associatedPins[1].direction         = "INPUT";
GPIO3.associatedPins[1].interruptEn       = true;
GPIO3.associatedPins[1].$name             = "FONT_RIGHT_A";
GPIO3.associatedPins[1].internalResistor  = "PULL_DOWN";
GPIO3.associatedPins[1].assignedPort      = "PORTB";
GPIO3.associatedPins[1].assignedPin       = "9";
GPIO3.associatedPins[1].interruptPriority = "1";
GPIO3.associatedPins[1].polarity          = "RISE";
GPIO3.associatedPins[1].pin.$assign       = "PB9";

GPIO4.$name                              = "SPD_READER_B";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name            = "FONT_LEFT_B";
GPIO4.associatedPins[0].direction        = "INPUT";
GPIO4.associatedPins[0].ioStructure      = "SD";
GPIO4.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[0].assignedPort     = "PORTB";
GPIO4.associatedPins[0].assignedPin      = "10";
GPIO4.associatedPins[1].$name            = "FONT_RIGHT_B";
GPIO4.associatedPins[1].direction        = "INPUT";
GPIO4.associatedPins[1].ioStructure      = "SD";
GPIO4.associatedPins[1].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[1].assignedPort     = "PORTB";
GPIO4.associatedPins[1].assignedPin      = "8";
GPIO4.associatedPins[1].pin.$assign      = "PB8";

GPIO5.$name                              = "Track";
GPIO5.associatedPins.create(7);
GPIO5.associatedPins[0].$name            = "L1";
GPIO5.associatedPins[0].direction        = "INPUT";
GPIO5.associatedPins[0].assignedPort     = "PORTA";
GPIO5.associatedPins[0].assignedPin      = "8";
GPIO5.associatedPins[0].pin.$assign      = "PA8";
GPIO5.associatedPins[1].$name            = "L2";
GPIO5.associatedPins[1].direction        = "INPUT";
GPIO5.associatedPins[1].assignedPort     = "PORTA";
GPIO5.associatedPins[1].assignedPin      = "28";
GPIO5.associatedPins[1].internalResistor = "PULL_UP";
GPIO5.associatedPins[2].$name            = "L3";
GPIO5.associatedPins[2].assignedPort     = "PORTA";
GPIO5.associatedPins[2].assignedPin      = "31";
GPIO5.associatedPins[2].direction        = "INPUT";
GPIO5.associatedPins[2].internalResistor = "PULL_UP";
GPIO5.associatedPins[3].$name            = "L4";
GPIO5.associatedPins[3].direction        = "INPUT";
GPIO5.associatedPins[3].assignedPort     = "PORTA";
GPIO5.associatedPins[3].assignedPin      = "25";
GPIO5.associatedPins[4].$name            = "L5";
GPIO5.associatedPins[4].direction        = "INPUT";
GPIO5.associatedPins[4].assignedPort     = "PORTA";
GPIO5.associatedPins[4].assignedPin      = "22";
GPIO5.associatedPins[5].$name            = "L6";
GPIO5.associatedPins[5].direction        = "INPUT";
GPIO5.associatedPins[5].assignedPort     = "PORTB";
GPIO5.associatedPins[5].assignedPin      = "20";
GPIO5.associatedPins[6].$name            = "L7";
GPIO5.associatedPins[6].direction        = "INPUT";
GPIO5.associatedPins[6].assignedPort     = "PORTA";
GPIO5.associatedPins[6].assignedPin      = "24";

I2C1.basicEnableController             = true;
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.$name                             = "I2C_OLED";
I2C1.intController                     = ["NACK","RX_DONE","TX_DONE"];
I2C1.peripheral.sdaPin.$assign         = "PA0";
I2C1.peripheral.sclPin.$assign         = "PA1";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";

PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.timerStartTimer                    = true;
PWM1.clockPrescale                      = 40;
PWM1.$name                              = "MotorAFront";
PWM1.timerCount                         = 100;
PWM1.peripheral.$assign                 = "TIMG7";
PWM1.peripheral.ccp0Pin.$assign         = "PB15";
PWM1.peripheral.ccp1Pin.$assign         = "PB16";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle            = 1;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.dutyCycle            = 1;
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

PWM2.$name                              = "MotorBFront";
PWM2.clockPrescale                      = 40;
PWM2.timerStartTimer                    = true;
PWM2.pwmMode                            = "EDGE_ALIGN_UP";
PWM2.timerCount                         = 100;
PWM2.peripheral.$assign                 = "TIMG6";
PWM2.peripheral.ccp0Pin.$assign         = "PB2";
PWM2.peripheral.ccp1Pin.$assign         = "PB3";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_0.dutyCycle            = 1;
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.PWM_CHANNEL_1.dutyCycle            = 1;
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric10";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric12";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable      = true;
SYSTICK.interruptEnable   = true;
SYSTICK.systickEnable     = true;
SYSTICK.period            = 80000;
SYSTICK.interruptPriority = "0";

TIMER1.$name            = "TIMER_0";
TIMER1.timerClkPrescale = 256;
TIMER1.timerMode        = "PERIODIC";
TIMER1.interrupts       = ["ZERO"];
TIMER1.timerPeriod      = "10 ms";

ProjectConfig.genLibIQ = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PB1";
GPIO1.associatedPins[1].pin.$suggestSolution       = "PA23";
GPIO1.associatedPins[2].pin.$suggestSolution       = "PB14";
GPIO1.associatedPins[3].pin.$suggestSolution       = "PA7";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PA26";
GPIO2.associatedPins[1].pin.$suggestSolution       = "PB24";
GPIO2.associatedPins[2].pin.$suggestSolution       = "PA27";
GPIO4.associatedPins[0].pin.$suggestSolution       = "PB10";
GPIO5.associatedPins[1].pin.$suggestSolution       = "PA28";
GPIO5.associatedPins[2].pin.$suggestSolution       = "PA31";
GPIO5.associatedPins[3].pin.$suggestSolution       = "PA25";
GPIO5.associatedPins[4].pin.$suggestSolution       = "PA22";
GPIO5.associatedPins[5].pin.$suggestSolution       = "PB20";
GPIO5.associatedPins[6].pin.$suggestSolution       = "PA24";
I2C1.peripheral.$suggestSolution                   = "I2C0";
TIMER1.peripheral.$suggestSolution                 = "TIMA0";
