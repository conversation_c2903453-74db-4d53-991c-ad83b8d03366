#ifndef __Task_App_h
#define __Task_App_h

#include "SysConfig.h"

extern int16_t Data_MotorEncoder[2];

// 在现有变量声明后添加
extern uint8_t Data_GraySensor_HardI2C_Input[8];
extern _iq Data_Motor_TarSpeed; //目标基础速度
;



// 在现有函数声明后添加
void Task_GraySensor_HardI2C(void *para);

void Task_Init(void);


// 电机配置验证函数声明
void Motor_Configuration_Verify(void);
void Motor_Direction_Test(void);
void Motor_PWM_Response_Test(void);

#endif