#include "Tracker.h"

#define DIS_INRERVAL _IQ(1.5) //传感器间距

uint8_t ret;
uint8_t tick[7];
bool is_turning = false;
static uint32_t turn_start_time = 0;

/**
 * @brief 读取循迹传感器并计算位置偏差
 * 
 * @param tck_ptr 8路传感器数据数组指针
 * @param offset_ptr 位置偏差值指针 (单位:cm, 负值表示偏左，正值表示偏右)
 * @return true 成功读取并计算偏差 
 * @return false 读取失败或参数错误
 */
bool Tracker_Read(uint8_t *tck_ptr, _iq *offset_ptr)
{
	uint8_t i;
    uint8_t test;

    if (tck_ptr == NULL || offset_ptr == NULL) return false;

    // 读取GPIO原始值（位掩码）
    uint32_t raw_values[7];
    raw_values[0] = DL_GPIO_readPins(Track_L1_PORT, Track_L1_PIN);
    raw_values[1] = DL_GPIO_readPins(Track_L2_PORT, Track_L2_PIN);
    raw_values[2] = DL_GPIO_readPins(Track_L3_PORT, Track_L3_PIN);
    raw_values[3] = DL_GPIO_readPins(Track_L4_PORT, Track_L4_PIN);
    raw_values[4] = DL_GPIO_readPins(Track_L5_PORT, Track_L5_PIN);
    raw_values[5] = DL_GPIO_readPins(Track_L6_PORT, Track_L6_PIN);
    raw_values[6] = DL_GPIO_readPins(Track_L7_PORT, Track_L7_PIN);

    // 转换为数字值（0或1）
    tick[0] = (raw_values[0] != 0) ? 0 : 1;
    tick[1] = (raw_values[1] != 0) ? 0 : 1;
    tick[2] = (raw_values[2] != 0) ? 0 : 1;
    tick[3] = (raw_values[3] != 0) ? 0 : 1;
    tick[4] = (raw_values[4] != 0) ? 0 : 1;
    tick[5] = (raw_values[5] != 0) ? 0 : 1;
    tick[6] = (raw_values[6] != 0) ? 0 : 1;

    // 显示调试信息
    OLED_Printf(0, 16*0, 8, "tick:%d%d%d%d%d%d%d", tick[0],tick[1],tick[2],tick[3],tick[4],tick[5],tick[6]);

    if(tick[0]==1 && tick[1]==1 && tick[2]==1 && tick[3]==0 &&
    tick[4]==0 && tick[5]==0 && tick[6]==0)
    {
        if (!is_turning) {
            is_turning = true;
            turn_start_time = Sys_GetTick();
            LED1_ON();
        }
        
        Motor_Font_Left.Motor_PID_Instance.Target = 2;
        Motor_Font_Right.Motor_PID_Instance.Target = 5;  // 提高转弯速度
        Data_Motor_TarSpeed=0;
        
    }
    else if (is_turning) {
        // 1秒后检查是否完成转弯
        if (Sys_GetTick() - turn_start_time > 500) {
            if (tick[3] == 0 || tick[2]==0 || tick[4]==0) {  // 中心传感器重新检测到线
                is_turning = false;
                LED1_OFF();
            }
        }
    }
    else if (!is_turning){
        LED1_OFF();
        
        Data_Motor_TarSpeed=_IQ(5);

        for (i = 0; i < 7; ++i) {
            tck_ptr[i]=tick[i];
        }


        _iq pos_sum = _IQ(0);
        uint8_t cnt = 0;

        // 加权平均法计算位置
        for (uint8_t i = 0; i < 7; i++)
        {
            if (tck_ptr[i] == TRACK_ON)
            {
                // 传感器位置权重: 0,1,2,3,4,5,6,7 对应 -5.25,-3.75,-2.25,-0.75,0.75,2.25,3.75,5.25 cm
                _iq sensor_pos = _IQmpy(_IQ(i - 3), DIS_INRERVAL);
                pos_sum += sensor_pos;
                cnt++;
            }
        }

        if (cnt == 0)
        {
            // 没有检测到线，保持上次偏差值
            return false;
        }

        // 计算加权平均位置偏差
        *offset_ptr = _IQdiv(pos_sum, _IQ(cnt));
    }
    return true;
}




