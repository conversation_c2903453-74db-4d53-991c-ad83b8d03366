/**
 * @file Task_App.c
 * <AUTHOR> name void Task_OLED(void *para)
 * @brief 任务实现层
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Task_App.h"

#define INDEX 1.5f //转向调试系数 

#define CAMERA_MAX_ERROR_COUNT 5 //摄像头最大错误次数
#define TRACK_WIDTH 2 //循迹黑线宽度
#define TRACK_ON    1 //在黑线上
#define TRACK_OFF   0 //不在黑线

static uint32_t lost_time = 0;
uint8_t current_path = 0;  // 0:A->C, 1:C->B, 2:B->D, 3:D->A, 4:完成 (全局变量，供OLED显示)


/*Data Motor*/
_iq Data_Motor_TarSpeed = _IQ(0); //目标基础速度
int16_t Data_MotorEncoder[2] = {0}; //编码器计数数组：[0]=右电机, [1]=左电机
MOTOR_Def_t *Motor[2] = {&Motor_Font_Left, &Motor_Font_Right}; //电机实例

/*Data Tracker*/
uint8_t Data_Tracker_Input[7] = {TRACK_OFF}; //循迹模块的输入值
_iq Data_Tracker_Offset = _IQ(0); //循迹偏差
PID_Def_t Data_Tracker_PID; //转向环PID


// 灰度传感器模拟值、数字值、归一化值
unsigned short Analog[8]={0};

uint8_t HD_count;
uint8_t Motor_Flag=0;

/*Test*/
bool Flag_LED = false;

void Task_Motor_PID(void *para);
void Task_OLED(void *para);
void Task_Tracker(void *para);
void Task_Key(void *para);

void Task_Init(void)
{
    Motor_Start(); //开启电机
    OLED_Init(); //OLED初始化

    Interrupt_Init(); //中断初始化

    Task_Add("Motor", Task_Motor_PID, 10, NULL, 0);
    Task_Add("Key", Task_Key, 20, NULL, 2);
    Task_Add("OLED", Task_OLED, 1000, NULL, 3);
    Task_Add("Tracker", Task_Tracker, 10, NULL, 1);
}


void Task_OLED(void *para)
{
    // OLED_ShowNum(0, 16*1, Data_Motor_TarSpeed, 8, 3);
    // OLED
    OLED_Printf(0, 16*1, 8, "S:%d", Data_Motor_TarSpeed);
    OLED_Printf(0, 16*2, 8, "SL:%4.2f SR:%4.2f",Motor_Font_Left.Motor_PID_Instance.Target,
                                         Motor_Font_Right.Motor_PID_Instance.Target);

    OLED_Printf(0, 16*3, 8, "SL:%4.2f SR:%4.2f",Motor_Font_Left.Motor_PID_Instance.Acutal_Now,
                                         Motor_Font_Right.Motor_PID_Instance.Acutal_Now);
}

//按键 20ms
void Task_Key(void *para)
{
    static uint8_t Key_Old;
    uint8_t Key_Temp = Key_Read();
    uint8_t Key_Val = (Key_Temp ^ Key_Old) & Key_Temp;
    Key_Old = Key_Temp;

    if (Key_Val==2)
    {
        Motor_Flag=!Motor_Flag;
    }
    else if(Key_Val==3)
    {
        Data_Motor_TarSpeed+=_IQ(5);
        LED1_OFF();
        LED2_ON();
        LED3_OFF();
        LED4_ON();
    }
    else if(Key_Val==4)
    {
        Data_Motor_TarSpeed=0;
        LED1_OFF();
        LED2_OFF();
        LED3_ON();
        LED4_OFF();
    }
}



//电机PID调控 50ms
void Task_Motor_PID(void *para)
{
    if(Motor_Flag)
    {
        //获取电机速度
        for (uint8_t i = 0; i < 2; i++)
        {
            Motor_GetSpeed(Motor[i], 20);
        }

        //差速转向控制 - 使用自适应修正算法
        _iq Steering_Adjustment = Data_Tracker_Offset;

        // 左轮：偏右时减速，偏左时加速
        _iq Left_Speed = Data_Motor_TarSpeed+ Steering_Adjustment;
        // 右轮：偏右时加速，偏左时减速
        _iq Right_Speed = Data_Motor_TarSpeed- Steering_Adjustment;
        
        if(!is_turning)
        {
            // 设置目标速度
            Motor_Font_Left.Motor_PID_Instance.Target =  _IQtoF(Left_Speed);
            Motor_Font_Right.Motor_PID_Instance.Target =  _IQtoF(Right_Speed);
        }

        //PID 计算
        for (uint8_t i = 0; i < 2; i++)
        {
            PID_SProsc(&Motor[i]->Motor_PID_Instance);
        }
    }
    else if(!Motor_Flag)
    {
        Motor_Font_Left.Motor_PID_Instance.Out = 0;  // 右电机30%速度
        Motor_Font_Right.Motor_PID_Instance.Out = 0;  // 右电机30%速度
    }
    // 设置电机PWM输出
    for (uint8_t i = 0; i < 2; i++)
    {
        float output = Motor[i]->Motor_PID_Instance.Out;
        Motor_SetDuty(Motor[i], output);
    }
}


//灰度传感器读取、计算偏差 20ms
void Task_Tracker(void *para)
{
    const _iq Filter_Value = _IQ(0.7); //滤波系数
    _iq Temp = _IQ(0); 
    bool res = Tracker_Read(Data_Tracker_Input, &Temp); 
    if (res == true)
    {
        Data_Tracker_Offset = _IQmpy(Temp, Filter_Value) + _IQmpy((_IQ(1) - Filter_Value), Data_Tracker_Offset);
    }
}


