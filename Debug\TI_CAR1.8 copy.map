******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 00:03:45 2025

OUTPUT FILE NAME:   <TI_CAR1.8 copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003f25


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005230  0001add0  R  X
  SRAM                  20200000   00008000  000009b6  0000764a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000047c8   000047c8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004708   00004708    r-x .text
000047d0    000047d0    00000a68   00000a68    r--
  000047d0    000047d0    00000a10   00000a10    r-- .rodata
  000051e0    000051e0    00000058   00000058    r-- .cinit
20200000    20200000    000007b6   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    000002f8   00000000    rw- .bss
  202006f8    202006f8    000000be   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004708     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000002b8     Tracker.o (.text.Tracker_Read)
                  00000d48    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000f68    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001144    000001d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001314    000001b0     Task.o (.text.Task_Start)
                  000014c4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001656    00000002     Task.o (.text.Task_IdleFunction)
                  00001658    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001794    00000134            : qsort.c.obj (.text.qsort)
                  000018c8    00000130     OLED.o (.text.OLED_ShowChar)
                  000019f8    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001b18    00000118     Task_App.o (.text.Task_Motor_PID)
                  00001c30    00000110     OLED.o (.text.OLED_Init)
                  00001d40    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001e4c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001f58    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000205c    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00002144    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002228    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002304    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000023dc    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000024b4    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002588    000000d0     Task_App.o (.text.Task_Key)
                  00002658    000000c4     PID.o (.text.PID_SProsc)
                  0000271c    000000b4     Task.o (.text.Task_Add)
                  000027d0    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002880    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002922    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002924    0000009c     Task_App.o (.text.Task_OLED)
                  000029c0    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00002a58    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002ae4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002b70    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002bfc    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002c88    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002d0c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002d88    00000074     Motor.o (.text.Motor_Start)
                  00002dfc    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002e70    0000006e     OLED.o (.text.OLED_ShowString)
                  00002ede    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002f48    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002fb0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003016    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003018    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  0000307c    00000064     Key_Led.o (.text.Key_Read)
                  000030e0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003142    00000002     --HOLE-- [fill = 0]
                  00003144    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000031a6    00000002     --HOLE-- [fill = 0]
                  000031a8    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003208    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003268    00000060     Task_App.o (.text.Task_Init)
                  000032c8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003326    00000002     --HOLE-- [fill = 0]
                  00003328    0000005c     Task_App.o (.text.Task_Tracker)
                  00003384    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000033e0    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  0000343c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003494    00000058            : _printfi.c.obj (.text._pconv_f)
                  000034ec    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003542    00000002     --HOLE-- [fill = 0]
                  00003544    00000054     Motor.o (.text.CalculateDutyValue)
                  00003598    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000035ec    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000363e    00000002     --HOLE-- [fill = 0]
                  00003640    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00003690    00000050     Interrupt.o (.text.Interrupt_Init)
                  000036e0    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003730    0000004c     OLED.o (.text.OLED_Printf)
                  0000377c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000037c6    00000048     Motor.o (.text.Motor_GetSpeed)
                  0000380e    00000002     --HOLE-- [fill = 0]
                  00003810    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00003858    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000389c    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000038e0    00000044     Motor.o (.text.SetPWMValue)
                  00003924    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00003966    00000002     --HOLE-- [fill = 0]
                  00003968    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000039a8    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000039e8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003a28    0000003e     Task.o (.text.Task_CMP)
                  00003a66    00000002     --HOLE-- [fill = 0]
                  00003a68    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003aa4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003ae0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003b1c    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003b58    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003b94    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003bd0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003c0c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003c46    00000002     --HOLE-- [fill = 0]
                  00003c48    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003c82    00000002     --HOLE-- [fill = 0]
                  00003c84    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003cb8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003cec    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00003d20    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003d50    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003d80    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003dac    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003dd8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003e04    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003e30    0000002a     PID.o (.text.PID_Init)
                  00003e5a    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003e82    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003eaa    00000002     --HOLE-- [fill = 0]
                  00003eac    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003ed4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003efc    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003f24    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003f4c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003f72    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003f98    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003fbc    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003fe0    00000022     PID.o (.text.PID_SetParams)
                  00004002    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004024    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004044    00000020     SysTick.o (.text.Delay)
                  00004064    00000020     main.o (.text.main)
                  00004084    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000040a2    00000002     --HOLE-- [fill = 0]
                  000040a4    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000040c0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000040dc    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000040f8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004114    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00004130    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000414c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004168    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004184    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000041a0    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000041bc    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000041d8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000041f4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004210    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  0000422c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004248    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004264    0000001c     Task_App.o (.text.TIMA0_IRQHandler)
                  00004280    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004298    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000042b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000042c8    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000042e0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000042f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004310    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004328    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004340    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004358    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00004370    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  00004388    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000043a0    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000043b8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000043d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000043e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004400    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004418    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004430    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004448    00000018     OLED.o (.text.DL_I2C_reset)
                  00004460    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004478    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004490    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000044a8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000044c0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000044d8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000044f0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004508    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004520    00000018     Interrupt.o (.text.DL_Timer_startCounter)
                  00004538    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004550    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004568    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  0000457e    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00004594    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000045aa    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  000045c0    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000045d6    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000045ea    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000045fe    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  00004612    00000002     --HOLE-- [fill = 0]
                  00004614    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004628    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000463c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004650    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004664    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004678    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000468c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000046a0    00000012     Task_App.o (.text.DL_Timer_getPendingInterrupt)
                  000046b2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000046c4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000046d6    00000002     --HOLE-- [fill = 0]
                  000046d8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000046e8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000046f8    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004708    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004718    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004726    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004734    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004742    00000002     --HOLE-- [fill = 0]
                  00004744    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004750    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000475a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004764    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004774    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000477e    0000000a            : vsprintf.c.obj (.text._outc)
                  00004788    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004790    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004798    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000047a0    00000006     libc.a : exit.c.obj (.text:abort)
                  000047a6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000047aa    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000047ae    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000047b2    00000002     --HOLE-- [fill = 0]
                  000047b4    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000047c4    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000051e0    00000058     
                  000051e0    00000032     (.cinit..data.load) [load image, compression = lzss]
                  00005212    00000002     --HOLE-- [fill = 0]
                  00005214    0000000c     (__TI_handler_table)
                  00005220    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005228    00000010     (__TI_cinit_table)

.rodata    0    000047d0    00000a10     
                  000047d0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004dc0    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004fe8    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00004ff0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000050f1    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00005132    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005134    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000515c    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00005170    00000014     Tracker.o (.rodata.str1.18388326728890721169.1)
                  00005184    00000012     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00005196    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000051a7    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000051b8    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  000051c0    00000008     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000051c8    00000005     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000051cd    00000005     Task_App.o (.rodata.str1.8896853068034818020.1)
                  000051d2    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  000051d6    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  000051d9    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  000051dc    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  000051df    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    000002f8     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000bc     (.common:gTIMER_0Backup)
                  202005ac    000000a0     (.common:gMotorAFrontBackup)
                  2020064c    000000a0     (.common:gMotorBFrontBackup)
                  202006ec    00000007     (.common:tick)
                  202006f3    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  202006f4    00000004     (.common:ExISR_Flag)

.data      0    202006f8    000000be     UNINITIALIZED
                  202006f8    00000048     Motor.o (.data.Motor_Font_Left)
                  20200740    00000048     Motor.o (.data.Motor_Font_Right)
                  20200788    00000008     Task_App.o (.data.Motor)
                  20200790    00000007     Task_App.o (.data.Data_Tracker_Input)
                  20200797    00000001     Task_App.o (.data.Motor_Flag)
                  20200798    00000004     Task_App.o (.data.Data_MotorEncoder)
                  2020079c    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202007a0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202007a4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202007a8    00000004     SysTick.o (.data.delayTick)
                  202007ac    00000004     Tracker.o (.data.turn_start_time)
                  202007b0    00000004     SysTick.o (.data.uwTick)
                  202007b4    00000001     Task.o (.data.Task_Num)
                  202007b5    00000001     Tracker.o (.data.is_turning)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2530    87        508    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2570    279       508    
                                                               
    .\APP\Src\
       Task_App.o                     922     40        29     
       Interrupt.o                    462     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         1384    40        33     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         676     0         241    
       Tracker.o                      762     20        12     
       Motor.o                        576     0         144    
       PID.o                          272     0         0      
       Key_Led.o                      122     0         0      
       SysTick.o                      84      0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4350    2092      405    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_i2c.o                       132     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1118    0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2630    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       86        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   18152   2853      2486   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005228 records: 2, size/record: 8, table size: 16
	.data: load addr=000051e0, load size=00000032 bytes, run addr=202006f8, run size=000000be bytes, compression=lzss
	.bss: load addr=00005220, load size=00000008 bytes, run addr=20200400, run size=000002f8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005214 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000014c5     00004764     00004762   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003f25     000047b4     000047ae   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000047a7  ADC0_IRQHandler                      
000047a7  ADC1_IRQHandler                      
000047a7  AES_IRQHandler                       
000047aa  C$$EXIT                              
000047a7  CANFD0_IRQHandler                    
000047a7  DAC0_IRQHandler                      
00004751  DL_Common_delayCycles                
000032c9  DL_I2C_fillControllerTXFIFO          
00003f73  DL_I2C_setClockConfig                
00002229  DL_SYSCTL_configSYSPLL               
00003019  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003859  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001f59  DL_Timer_initFourCCPWMMode           
0000205d  DL_Timer_initTimerMode               
0000422d  DL_Timer_setCaptCompUpdateMethod     
00004509  DL_Timer_setCaptureCompareOutCtl     
000046e9  DL_Timer_setCaptureCompareValue      
00004249  DL_Timer_setClockConfig              
000047a7  DMA_IRQHandler                       
20200798  Data_MotorEncoder                    
2020079c  Data_Motor_TarSpeed                  
20200790  Data_Tracker_Input                   
202007a0  Data_Tracker_Offset                  
000047a7  Default_Handler                      
00004045  Delay                                
202006f4  ExISR_Flag                           
000047a7  GROUP0_IRQHandler                    
000027d1  GROUP1_IRQHandler                    
000047ab  HOSTexit                             
000047a7  HardFault_Handler                    
000047a7  I2C0_IRQHandler                      
000047a7  I2C1_IRQHandler                      
00002edf  I2C_OLED_Clear                       
00003b1d  I2C_OLED_Set_Pos                     
000029c1  I2C_OLED_WR_Byte                     
000031a9  I2C_OLED_i2c_sda_unlock              
00003691  Interrupt_Init                       
0000307d  Key_Read                             
20200788  Motor                                
20200797  Motor_Flag                           
202006f8  Motor_Font_Left                      
20200740  Motor_Font_Right                     
000037c7  Motor_GetSpeed                       
000024b5  Motor_SetDuty                        
00002d89  Motor_Start                          
000047a7  NMI_Handler                          
00001c31  OLED_Init                            
00003731  OLED_Printf                          
000018c9  OLED_ShowChar                        
00002e71  OLED_ShowString                      
00003e31  PID_Init                             
00002659  PID_SProsc                           
00003fe1  PID_SetParams                        
000047a7  PendSV_Handler                       
000047a7  RTC_IRQHandler                       
000047af  Reset_Handler                        
000047a7  SPI0_IRQHandler                      
000047a7  SPI1_IRQHandler                      
000047a7  SVC_Handler                          
00001145  SYSCFG_DL_GPIO_init                  
00003209  SYSCFG_DL_I2C_OLED_init              
00002a59  SYSCFG_DL_MotorAFront_init           
00002ae5  SYSCFG_DL_MotorBFront_init           
00003599  SYSCFG_DL_SYSCTL_init                
000046f9  SYSCFG_DL_SYSTICK_init               
00003ced  SYSCFG_DL_TIMER_0_init               
0000389d  SYSCFG_DL_init                       
00002b71  SYSCFG_DL_initPower                  
00004789  SysTick_Handler                      
00003efd  SysTick_Increasment                  
00004745  Sys_GetTick                          
00004265  TIMA0_IRQHandler                     
000047a7  TIMA1_IRQHandler                     
000047a7  TIMG0_IRQHandler                     
000047a7  TIMG12_IRQHandler                    
000047a7  TIMG6_IRQHandler                     
000047a7  TIMG7_IRQHandler                     
000047a7  TIMG8_IRQHandler                     
000046b3  TI_memcpy_small                      
00004735  TI_memset_small                      
0000271d  Task_Add                             
00001657  Task_IdleFunction                    
00003269  Task_Init                            
00002589  Task_Key                             
00001b19  Task_Motor_PID                       
00002925  Task_OLED                            
00001315  Task_Start                           
00003329  Task_Tracker                         
00000a91  Tracker_Read                         
000047a7  UART0_IRQHandler                     
000047a7  UART1_IRQHandler                     
000047a7  UART2_IRQHandler                     
000047a7  UART3_IRQHandler                     
00001d41  _IQ24div                             
00003d81  _IQ24mpy                             
00003d21  _IQ24toF                             
000050f1  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005228  __TI_CINIT_Base                      
00005238  __TI_CINIT_Limit                     
00005238  __TI_CINIT_Warm                      
00005214  __TI_Handler_Table_Base              
00005220  __TI_Handler_Table_Limit             
00003bd1  __TI_auto_init_nobinit_nopinit       
00002d0d  __TI_decompress_lzss                 
000046c5  __TI_decompress_none                 
0000343d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000045c1  __TI_zero_init_nomemset              
000014cf  __adddf3                             
000023e7  __addsf3                             
00004ff0  __aeabi_ctype_table_                 
00004ff0  __aeabi_ctype_table_C                
0000377d  __aeabi_d2iz                         
00003925  __aeabi_d2uiz                        
000014cf  __aeabi_dadd                         
000030e1  __aeabi_dcmpeq                       
0000311d  __aeabi_dcmpge                       
00003131  __aeabi_dcmpgt                       
00003109  __aeabi_dcmple                       
000030f5  __aeabi_dcmplt                       
00001e4d  __aeabi_ddiv                         
00002145  __aeabi_dmul                         
000014c5  __aeabi_dsub                         
202007a4  __aeabi_errno                        
00004791  __aeabi_errno_addr                   
000039a9  __aeabi_f2d                          
000023e7  __aeabi_fadd                         
00003145  __aeabi_fcmpeq                       
00003181  __aeabi_fcmpge                       
00003195  __aeabi_fcmpgt                       
0000316d  __aeabi_fcmple                       
00003159  __aeabi_fcmplt                       
00002bfd  __aeabi_fmul                         
000023dd  __aeabi_fsub                         
00003dd9  __aeabi_i2d                          
00003b59  __aeabi_i2f                          
000034ed  __aeabi_idiv                         
00002923  __aeabi_idiv0                        
000034ed  __aeabi_idivmod                      
00003017  __aeabi_ldiv0                        
00004085  __aeabi_llsl                         
00003fbd  __aeabi_lmul                         
00004799  __aeabi_memcpy                       
00004799  __aeabi_memcpy4                      
00004799  __aeabi_memcpy8                      
00004719  __aeabi_memset                       
00004719  __aeabi_memset4                      
00004719  __aeabi_memset8                      
00003f99  __aeabi_ui2d                         
00003969  __aeabi_uidiv                        
00003969  __aeabi_uidivmod                     
00004679  __aeabi_uldivmod                     
00004085  __ashldi3                            
ffffffff  __binit__                            
00002f49  __cmpdf2                             
00003c0d  __cmpsf2                             
00001e4d  __divdf3                             
00002f49  __eqdf2                              
00003c0d  __eqsf2                              
000039a9  __extendsfdf2                        
0000377d  __fixdfsi                            
00003925  __fixunsdfsi                         
00003dd9  __floatsidf                          
00003b59  __floatsisf                          
00003f99  __floatunsidf                        
00002dfd  __gedf2                              
00003b95  __gesf2                              
00002dfd  __gtdf2                              
00003b95  __gtsf2                              
00002f49  __ledf2                              
00003c0d  __lesf2                              
00002f49  __ltdf2                              
00003c0d  __ltsf2                              
UNDEFED   __mpu_init                           
00002145  __muldf3                             
00003fbd  __muldi3                             
00003c49  __muldsi3                            
00002bfd  __mulsf3                             
00002f49  __nedf2                              
00003c0d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000014c5  __subdf3                             
000023dd  __subsf3                             
00002881  __udivmoddi4                         
00003f25  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
000047c5  _system_pre_init                     
000047a1  abort                                
00004dc0  asc2_0806                            
000047d0  asc2_1608                            
000039e9  atoi                                 
ffffffff  binit                                
202007a8  delayTick                            
00003385  frexp                                
00003385  frexpl                               
202005ac  gMotorAFrontBackup                   
2020064c  gMotorBFrontBackup                   
202004f0  gTIMER_0Backup                       
00000000  interruptVectors                     
202007b5  is_turning                           
00002305  ldexp                                
00002305  ldexpl                               
00004065  main                                 
00004003  memccpy                              
00001795  qsort                                
00002305  scalbn                               
00002305  scalbnl                              
202006ec  tick                                 
202007b0  uwTick                               
00003e05  vsprintf                             
00004709  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  Tracker_Read                         
00001145  SYSCFG_DL_GPIO_init                  
00001315  Task_Start                           
000014c5  __aeabi_dsub                         
000014c5  __subdf3                             
000014cf  __adddf3                             
000014cf  __aeabi_dadd                         
00001657  Task_IdleFunction                    
00001795  qsort                                
000018c9  OLED_ShowChar                        
00001b19  Task_Motor_PID                       
00001c31  OLED_Init                            
00001d41  _IQ24div                             
00001e4d  __aeabi_ddiv                         
00001e4d  __divdf3                             
00001f59  DL_Timer_initFourCCPWMMode           
0000205d  DL_Timer_initTimerMode               
00002145  __aeabi_dmul                         
00002145  __muldf3                             
00002229  DL_SYSCTL_configSYSPLL               
00002305  ldexp                                
00002305  ldexpl                               
00002305  scalbn                               
00002305  scalbnl                              
000023dd  __aeabi_fsub                         
000023dd  __subsf3                             
000023e7  __addsf3                             
000023e7  __aeabi_fadd                         
000024b5  Motor_SetDuty                        
00002589  Task_Key                             
00002659  PID_SProsc                           
0000271d  Task_Add                             
000027d1  GROUP1_IRQHandler                    
00002881  __udivmoddi4                         
00002923  __aeabi_idiv0                        
00002925  Task_OLED                            
000029c1  I2C_OLED_WR_Byte                     
00002a59  SYSCFG_DL_MotorAFront_init           
00002ae5  SYSCFG_DL_MotorBFront_init           
00002b71  SYSCFG_DL_initPower                  
00002bfd  __aeabi_fmul                         
00002bfd  __mulsf3                             
00002d0d  __TI_decompress_lzss                 
00002d89  Motor_Start                          
00002dfd  __gedf2                              
00002dfd  __gtdf2                              
00002e71  OLED_ShowString                      
00002edf  I2C_OLED_Clear                       
00002f49  __cmpdf2                             
00002f49  __eqdf2                              
00002f49  __ledf2                              
00002f49  __ltdf2                              
00002f49  __nedf2                              
00003017  __aeabi_ldiv0                        
00003019  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000307d  Key_Read                             
000030e1  __aeabi_dcmpeq                       
000030f5  __aeabi_dcmplt                       
00003109  __aeabi_dcmple                       
0000311d  __aeabi_dcmpge                       
00003131  __aeabi_dcmpgt                       
00003145  __aeabi_fcmpeq                       
00003159  __aeabi_fcmplt                       
0000316d  __aeabi_fcmple                       
00003181  __aeabi_fcmpge                       
00003195  __aeabi_fcmpgt                       
000031a9  I2C_OLED_i2c_sda_unlock              
00003209  SYSCFG_DL_I2C_OLED_init              
00003269  Task_Init                            
000032c9  DL_I2C_fillControllerTXFIFO          
00003329  Task_Tracker                         
00003385  frexp                                
00003385  frexpl                               
0000343d  __TI_ltoa                            
000034ed  __aeabi_idiv                         
000034ed  __aeabi_idivmod                      
00003599  SYSCFG_DL_SYSCTL_init                
00003691  Interrupt_Init                       
00003731  OLED_Printf                          
0000377d  __aeabi_d2iz                         
0000377d  __fixdfsi                            
000037c7  Motor_GetSpeed                       
00003859  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000389d  SYSCFG_DL_init                       
00003925  __aeabi_d2uiz                        
00003925  __fixunsdfsi                         
00003969  __aeabi_uidiv                        
00003969  __aeabi_uidivmod                     
000039a9  __aeabi_f2d                          
000039a9  __extendsfdf2                        
000039e9  atoi                                 
00003b1d  I2C_OLED_Set_Pos                     
00003b59  __aeabi_i2f                          
00003b59  __floatsisf                          
00003b95  __gesf2                              
00003b95  __gtsf2                              
00003bd1  __TI_auto_init_nobinit_nopinit       
00003c0d  __cmpsf2                             
00003c0d  __eqsf2                              
00003c0d  __lesf2                              
00003c0d  __ltsf2                              
00003c0d  __nesf2                              
00003c49  __muldsi3                            
00003ced  SYSCFG_DL_TIMER_0_init               
00003d21  _IQ24toF                             
00003d81  _IQ24mpy                             
00003dd9  __aeabi_i2d                          
00003dd9  __floatsidf                          
00003e05  vsprintf                             
00003e31  PID_Init                             
00003efd  SysTick_Increasment                  
00003f25  _c_int00_noargs                      
00003f73  DL_I2C_setClockConfig                
00003f99  __aeabi_ui2d                         
00003f99  __floatunsidf                        
00003fbd  __aeabi_lmul                         
00003fbd  __muldi3                             
00003fe1  PID_SetParams                        
00004003  memccpy                              
00004045  Delay                                
00004065  main                                 
00004085  __aeabi_llsl                         
00004085  __ashldi3                            
0000422d  DL_Timer_setCaptCompUpdateMethod     
00004249  DL_Timer_setClockConfig              
00004265  TIMA0_IRQHandler                     
00004509  DL_Timer_setCaptureCompareOutCtl     
000045c1  __TI_zero_init_nomemset              
00004679  __aeabi_uldivmod                     
000046b3  TI_memcpy_small                      
000046c5  __TI_decompress_none                 
000046e9  DL_Timer_setCaptureCompareValue      
000046f9  SYSCFG_DL_SYSTICK_init               
00004709  wcslen                               
00004719  __aeabi_memset                       
00004719  __aeabi_memset4                      
00004719  __aeabi_memset8                      
00004735  TI_memset_small                      
00004745  Sys_GetTick                          
00004751  DL_Common_delayCycles                
00004789  SysTick_Handler                      
00004791  __aeabi_errno_addr                   
00004799  __aeabi_memcpy                       
00004799  __aeabi_memcpy4                      
00004799  __aeabi_memcpy8                      
000047a1  abort                                
000047a7  ADC0_IRQHandler                      
000047a7  ADC1_IRQHandler                      
000047a7  AES_IRQHandler                       
000047a7  CANFD0_IRQHandler                    
000047a7  DAC0_IRQHandler                      
000047a7  DMA_IRQHandler                       
000047a7  Default_Handler                      
000047a7  GROUP0_IRQHandler                    
000047a7  HardFault_Handler                    
000047a7  I2C0_IRQHandler                      
000047a7  I2C1_IRQHandler                      
000047a7  NMI_Handler                          
000047a7  PendSV_Handler                       
000047a7  RTC_IRQHandler                       
000047a7  SPI0_IRQHandler                      
000047a7  SPI1_IRQHandler                      
000047a7  SVC_Handler                          
000047a7  TIMA1_IRQHandler                     
000047a7  TIMG0_IRQHandler                     
000047a7  TIMG12_IRQHandler                    
000047a7  TIMG6_IRQHandler                     
000047a7  TIMG7_IRQHandler                     
000047a7  TIMG8_IRQHandler                     
000047a7  UART0_IRQHandler                     
000047a7  UART1_IRQHandler                     
000047a7  UART2_IRQHandler                     
000047a7  UART3_IRQHandler                     
000047aa  C$$EXIT                              
000047ab  HOSTexit                             
000047af  Reset_Handler                        
000047c5  _system_pre_init                     
000047d0  asc2_1608                            
00004dc0  asc2_0806                            
00004ff0  __aeabi_ctype_table_                 
00004ff0  __aeabi_ctype_table_C                
000050f1  _IQ6div_lookup                       
00005214  __TI_Handler_Table_Base              
00005220  __TI_Handler_Table_Limit             
00005228  __TI_CINIT_Base                      
00005238  __TI_CINIT_Limit                     
00005238  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gTIMER_0Backup                       
202005ac  gMotorAFrontBackup                   
2020064c  gMotorBFrontBackup                   
202006ec  tick                                 
202006f4  ExISR_Flag                           
202006f8  Motor_Font_Left                      
20200740  Motor_Font_Right                     
20200788  Motor                                
20200790  Data_Tracker_Input                   
20200797  Motor_Flag                           
20200798  Data_MotorEncoder                    
2020079c  Data_Motor_TarSpeed                  
202007a0  Data_Tracker_Offset                  
202007a4  __aeabi_errno                        
202007a8  delayTick                            
202007b0  uwTick                               
202007b5  is_turning                           
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[232 symbols]
