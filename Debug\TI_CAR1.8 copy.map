******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 21:21:42 2025

OUTPUT FILE NAME:   <TI_CAR1.8 copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003d6d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005008  0001aff8  R  X
  SRAM                  20200000   00008000  000008f5  0000770b  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005008   00005008    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000044f0   000044f0    r-x .text
  000045b0    000045b0    00000a00   00000a00    r-- .rodata
  00004fb0    00004fb0    00000058   00000058    r-- .cinit
20200000    20200000    000006f5   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    0000023c   00000000    rw- .bss
  2020063c    2020063c    000000b9   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000044f0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000240     Tracker.o (.text.Tracker_Read)
                  00000cd0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000ef0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000010cc    000001d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000129c    000001b0     Task.o (.text.Task_Start)
                  0000144c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000015de    00000002     Task.o (.text.Task_IdleFunction)
                  000015e0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000171c    00000134            : qsort.c.obj (.text.qsort)
                  00001850    00000130     OLED.o (.text.OLED_ShowChar)
                  00001980    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001aa0    00000110     OLED.o (.text.OLED_Init)
                  00001bb0    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001cbc    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001dc8    00000108     Task_App.o (.text.Task_Motor_PID)
                  00001ed0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001fd4    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000020b8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002194    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000226c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002344    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002418    000000d4     Task_App.o (.text.Task_Key)
                  000024ec    000000c4     PID.o (.text.PID_SProsc)
                  000025b0    000000b4     Task.o (.text.Task_Add)
                  00002664    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002714    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000027b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000027b8    0000009c     Task_App.o (.text.Task_OLED)
                  00002854    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000028ec    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002978    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002a04    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002a90    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002b14    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002b90    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002c0c    00000078     Task_App.o (.text.Task_Init)
                  00002c84    00000074     Motor.o (.text.Motor_Start)
                  00002cf8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002d6c    0000006e     OLED.o (.text.OLED_ShowString)
                  00002dda    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002e44    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002eac    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002f12    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002f14    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002f78    00000064     Key_Led.o (.text.Key_Read)
                  00002fdc    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000303e    00000002     --HOLE-- [fill = 0]
                  00003040    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000030a2    00000002     --HOLE-- [fill = 0]
                  000030a4    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003104    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003164    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000031c2    00000002     --HOLE-- [fill = 0]
                  000031c4    0000005c     Task_App.o (.text.Task_Tracker)
                  00003220    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000327c    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000032d8    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003330    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003388    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000033de    00000002     --HOLE-- [fill = 0]
                  000033e0    00000054     Motor.o (.text.CalculateDutyValue)
                  00003434    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003488    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000034da    00000002     --HOLE-- [fill = 0]
                  000034dc    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  0000352c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000357c    0000004c     OLED.o (.text.OLED_Printf)
                  000035c8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003612    00000048     Motor.o (.text.Motor_GetSpeed)
                  0000365a    00000002     --HOLE-- [fill = 0]
                  0000365c    00000048     OLED.o (.text.mspm0_i2c_disable)
                  000036a4    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000036e8    00000044     Motor.o (.text.SetPWMValue)
                  0000372c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000376e    00000002     --HOLE-- [fill = 0]
                  00003770    00000040     Interrupt.o (.text.Interrupt_Init)
                  000037b0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000037f0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003830    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003870    0000003e     Task.o (.text.Task_CMP)
                  000038ae    00000002     --HOLE-- [fill = 0]
                  000038b0    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000038ec    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003928    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003964    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000039a0    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000039dc    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003a18    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003a54    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003a8e    00000002     --HOLE-- [fill = 0]
                  00003a90    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003aca    00000002     --HOLE-- [fill = 0]
                  00003acc    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003b00    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003b34    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003b68    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003b98    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003bc8    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003bf4    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003c20    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003c4c    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003c78    0000002a     PID.o (.text.PID_Init)
                  00003ca2    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003cca    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003cf2    00000002     --HOLE-- [fill = 0]
                  00003cf4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003d1c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003d44    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003d6c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003d94    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003dba    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003de0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003e04    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003e28    00000022     PID.o (.text.PID_SetParams)
                  00003e4a    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003e6c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003e8c    00000020     SysTick.o (.text.Delay)
                  00003eac    00000020     main.o (.text.main)
                  00003ecc    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003eea    00000002     --HOLE-- [fill = 0]
                  00003eec    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003f08    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003f24    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00003f40    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003f5c    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00003f78    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003f94    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003fb0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00003fcc    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00003fe8    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00004004    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00004020    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000403c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004058    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004074    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004090    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000040a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000040c0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000040d8    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000040f0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004108    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004120    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004138    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004150    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004168    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00004180    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  00004198    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000041b0    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000041c8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000041e0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000041f8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004210    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004228    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004240    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004258    00000018     OLED.o (.text.DL_I2C_reset)
                  00004270    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004288    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000042a0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000042b8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000042d0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000042e8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004300    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004318    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004330    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004348    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004360    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004376    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000438c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000043a2    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  000043b8    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000043ce    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000043e2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000043f6    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  0000440a    00000002     --HOLE-- [fill = 0]
                  0000440c    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004420    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004434    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004448    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000445c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004470    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004484    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004498    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  000044aa    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000044bc    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000044cc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000044dc    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000044ec    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000044fc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000450a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004518    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004526    00000002     --HOLE-- [fill = 0]
                  00004528    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004534    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000453e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004548    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004558    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004562    0000000a            : vsprintf.c.obj (.text._outc)
                  0000456c    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004574    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000457c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004584    00000006     libc.a : exit.c.obj (.text:abort)
                  0000458a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000458e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004592    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004596    00000002     --HOLE-- [fill = 0]
                  00004598    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000045a8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000045ac    00000004     --HOLE-- [fill = 0]

.cinit     0    00004fb0    00000058     
                  00004fb0    00000031     (.cinit..data.load) [load image, compression = lzss]
                  00004fe1    00000003     --HOLE-- [fill = 0]
                  00004fe4    0000000c     (__TI_handler_table)
                  00004ff0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004ff8    00000010     (__TI_cinit_table)

.rodata    0    000045b0    00000a00     
                  000045b0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004ba0    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004dc8    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00004dd0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004ed1    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00004f12    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004f14    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004f3c    00000014     Tracker.o (.rodata.str1.18388326728890721169.1)
                  00004f50    00000012     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00004f62    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004f73    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004f84    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00004f8c    00000008     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00004f94    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00004f9a    00000005     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00004f9f    00000005     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00004fa4    00000004     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00004fa8    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00004fab    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00004fae    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    0000023c     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000007     (.common:tick)
                  20200637    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200638    00000004     (.common:ExISR_Flag)

.data      0    2020063c    000000b9     UNINITIALIZED
                  2020063c    00000048     Motor.o (.data.Motor_Font_Left)
                  20200684    00000048     Motor.o (.data.Motor_Font_Right)
                  202006cc    00000008     Task_App.o (.data.Motor)
                  202006d4    00000007     Task_App.o (.data.Data_Tracker_Input)
                  202006db    00000001     Task_App.o (.data.Motor_Flag)
                  202006dc    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006e0    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006e4    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006e8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202006ec    00000004     SysTick.o (.data.delayTick)
                  202006f0    00000004     SysTick.o (.data.uwTick)
                  202006f4    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2418    64        320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2458    256       320    
                                                               
    .\APP\Src\
       Task_App.o                     888     46        29     
       Interrupt.o                    422     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         1310    46        33     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         676     0         241    
       Motor.o                        576     0         144    
       Tracker.o                      642     20        7      
       PID.o                          272     0         0      
       Key_Led.o                      122     0         0      
       SysTick.o                      84      0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4230    2092      400    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       132     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         886     0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2630    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       85        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17614   2835      2293   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004ff8 records: 2, size/record: 8, table size: 16
	.data: load addr=00004fb0, load size=00000031 bytes, run addr=2020063c, run size=000000b9 bytes, compression=lzss
	.bss: load addr=00004ff0, load size=00000008 bytes, run addr=20200400, run size=0000023c bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004fe4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000144d     00004548     00004546   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003d6d     00004598     00004592   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000458b  ADC0_IRQHandler                      
0000458b  ADC1_IRQHandler                      
0000458b  AES_IRQHandler                       
0000458e  C$$EXIT                              
0000458b  CANFD0_IRQHandler                    
0000458b  DAC0_IRQHandler                      
00004535  DL_Common_delayCycles                
00003165  DL_I2C_fillControllerTXFIFO          
00003dbb  DL_I2C_setClockConfig                
000020b9  DL_SYSCTL_configSYSPLL               
00002f15  DL_SYSCTL_setHFCLKSourceHFXTParams   
000036a5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001ed1  DL_Timer_initFourCCPWMMode           
00004059  DL_Timer_setCaptCompUpdateMethod     
00004319  DL_Timer_setCaptureCompareOutCtl     
000044cd  DL_Timer_setCaptureCompareValue      
00004075  DL_Timer_setClockConfig              
0000458b  DMA_IRQHandler                       
202006dc  Data_MotorEncoder                    
202006e0  Data_Motor_TarSpeed                  
202006d4  Data_Tracker_Input                   
202006e4  Data_Tracker_Offset                  
0000458b  Default_Handler                      
00003e8d  Delay                                
20200638  ExISR_Flag                           
0000458b  GROUP0_IRQHandler                    
00002665  GROUP1_IRQHandler                    
0000458f  HOSTexit                             
0000458b  HardFault_Handler                    
0000458b  I2C0_IRQHandler                      
0000458b  I2C1_IRQHandler                      
00002ddb  I2C_OLED_Clear                       
00003965  I2C_OLED_Set_Pos                     
00002855  I2C_OLED_WR_Byte                     
000030a5  I2C_OLED_i2c_sda_unlock              
00003771  Interrupt_Init                       
00002f79  Key_Read                             
202006cc  Motor                                
202006db  Motor_Flag                           
2020063c  Motor_Font_Left                      
20200684  Motor_Font_Right                     
00003613  Motor_GetSpeed                       
00002345  Motor_SetDuty                        
00002c85  Motor_Start                          
0000458b  NMI_Handler                          
00001aa1  OLED_Init                            
0000357d  OLED_Printf                          
00001851  OLED_ShowChar                        
00002d6d  OLED_ShowString                      
00003c79  PID_Init                             
000024ed  PID_SProsc                           
00003e29  PID_SetParams                        
0000458b  PendSV_Handler                       
0000458b  RTC_IRQHandler                       
00004593  Reset_Handler                        
0000458b  SPI0_IRQHandler                      
0000458b  SPI1_IRQHandler                      
0000458b  SVC_Handler                          
000010cd  SYSCFG_DL_GPIO_init                  
00003105  SYSCFG_DL_I2C_OLED_init              
000028ed  SYSCFG_DL_MotorAFront_init           
00002979  SYSCFG_DL_MotorBFront_init           
00003435  SYSCFG_DL_SYSCTL_init                
000044dd  SYSCFG_DL_SYSTICK_init               
00003b35  SYSCFG_DL_init                       
00002b15  SYSCFG_DL_initPower                  
0000456d  SysTick_Handler                      
00003d45  SysTick_Increasment                  
00004529  Sys_GetTick                          
0000458b  TIMA0_IRQHandler                     
0000458b  TIMA1_IRQHandler                     
0000458b  TIMG0_IRQHandler                     
0000458b  TIMG12_IRQHandler                    
0000458b  TIMG6_IRQHandler                     
0000458b  TIMG7_IRQHandler                     
0000458b  TIMG8_IRQHandler                     
00004499  TI_memcpy_small                      
00004519  TI_memset_small                      
000025b1  Task_Add                             
000015df  Task_IdleFunction                    
00002c0d  Task_Init                            
00002419  Task_Key                             
00001dc9  Task_Motor_PID                       
000027b9  Task_OLED                            
0000129d  Task_Start                           
000031c5  Task_Tracker                         
00000a91  Tracker_Read                         
0000458b  UART0_IRQHandler                     
0000458b  UART1_IRQHandler                     
0000458b  UART2_IRQHandler                     
0000458b  UART3_IRQHandler                     
00001bb1  _IQ24div                             
00003bc9  _IQ24mpy                             
00003b69  _IQ24toF                             
00004ed1  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004ff8  __TI_CINIT_Base                      
00005008  __TI_CINIT_Limit                     
00005008  __TI_CINIT_Warm                      
00004fe4  __TI_Handler_Table_Base              
00004ff0  __TI_Handler_Table_Limit             
00003a19  __TI_auto_init_nobinit_nopinit       
00002b91  __TI_decompress_lzss                 
000044ab  __TI_decompress_none                 
000032d9  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000043b9  __TI_zero_init_nomemset              
00001457  __adddf3                             
00002277  __addsf3                             
00004dd0  __aeabi_ctype_table_                 
00004dd0  __aeabi_ctype_table_C                
000035c9  __aeabi_d2iz                         
0000372d  __aeabi_d2uiz                        
00001457  __aeabi_dadd                         
00002fdd  __aeabi_dcmpeq                       
00003019  __aeabi_dcmpge                       
0000302d  __aeabi_dcmpgt                       
00003005  __aeabi_dcmple                       
00002ff1  __aeabi_dcmplt                       
00001cbd  __aeabi_ddiv                         
00001fd5  __aeabi_dmul                         
0000144d  __aeabi_dsub                         
202006e8  __aeabi_errno                        
00004575  __aeabi_errno_addr                   
000037f1  __aeabi_f2d                          
00002277  __aeabi_fadd                         
00003041  __aeabi_fcmpeq                       
0000307d  __aeabi_fcmpge                       
00003091  __aeabi_fcmpgt                       
00003069  __aeabi_fcmple                       
00003055  __aeabi_fcmplt                       
00002a05  __aeabi_fmul                         
0000226d  __aeabi_fsub                         
00003c21  __aeabi_i2d                          
000039a1  __aeabi_i2f                          
00003389  __aeabi_idiv                         
000027b7  __aeabi_idiv0                        
00003389  __aeabi_idivmod                      
00002f13  __aeabi_ldiv0                        
00003ecd  __aeabi_llsl                         
00003e05  __aeabi_lmul                         
0000457d  __aeabi_memcpy                       
0000457d  __aeabi_memcpy4                      
0000457d  __aeabi_memcpy8                      
000044fd  __aeabi_memset                       
000044fd  __aeabi_memset4                      
000044fd  __aeabi_memset8                      
00003de1  __aeabi_ui2d                         
000037b1  __aeabi_uidiv                        
000037b1  __aeabi_uidivmod                     
00004471  __aeabi_uldivmod                     
00003ecd  __ashldi3                            
ffffffff  __binit__                            
00002e45  __cmpdf2                             
00003a55  __cmpsf2                             
00001cbd  __divdf3                             
00002e45  __eqdf2                              
00003a55  __eqsf2                              
000037f1  __extendsfdf2                        
000035c9  __fixdfsi                            
0000372d  __fixunsdfsi                         
00003c21  __floatsidf                          
000039a1  __floatsisf                          
00003de1  __floatunsidf                        
00002cf9  __gedf2                              
000039dd  __gesf2                              
00002cf9  __gtdf2                              
000039dd  __gtsf2                              
00002e45  __ledf2                              
00003a55  __lesf2                              
00002e45  __ltdf2                              
00003a55  __ltsf2                              
UNDEFED   __mpu_init                           
00001fd5  __muldf3                             
00003e05  __muldi3                             
00003a91  __muldsi3                            
00002a05  __mulsf3                             
00002e45  __nedf2                              
00003a55  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000144d  __subdf3                             
0000226d  __subsf3                             
00002715  __udivmoddi4                         
00003d6d  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
000045a9  _system_pre_init                     
00004585  abort                                
00004ba0  asc2_0806                            
000045b0  asc2_1608                            
00003831  atoi                                 
ffffffff  binit                                
202006ec  delayTick                            
00003221  frexp                                
00003221  frexpl                               
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
00002195  ldexp                                
00002195  ldexpl                               
00003ead  main                                 
00003e4b  memccpy                              
0000171d  qsort                                
00002195  scalbn                               
00002195  scalbnl                              
20200630  tick                                 
202006f0  uwTick                               
00003c4d  vsprintf                             
000044ed  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  Tracker_Read                         
000010cd  SYSCFG_DL_GPIO_init                  
0000129d  Task_Start                           
0000144d  __aeabi_dsub                         
0000144d  __subdf3                             
00001457  __adddf3                             
00001457  __aeabi_dadd                         
000015df  Task_IdleFunction                    
0000171d  qsort                                
00001851  OLED_ShowChar                        
00001aa1  OLED_Init                            
00001bb1  _IQ24div                             
00001cbd  __aeabi_ddiv                         
00001cbd  __divdf3                             
00001dc9  Task_Motor_PID                       
00001ed1  DL_Timer_initFourCCPWMMode           
00001fd5  __aeabi_dmul                         
00001fd5  __muldf3                             
000020b9  DL_SYSCTL_configSYSPLL               
00002195  ldexp                                
00002195  ldexpl                               
00002195  scalbn                               
00002195  scalbnl                              
0000226d  __aeabi_fsub                         
0000226d  __subsf3                             
00002277  __addsf3                             
00002277  __aeabi_fadd                         
00002345  Motor_SetDuty                        
00002419  Task_Key                             
000024ed  PID_SProsc                           
000025b1  Task_Add                             
00002665  GROUP1_IRQHandler                    
00002715  __udivmoddi4                         
000027b7  __aeabi_idiv0                        
000027b9  Task_OLED                            
00002855  I2C_OLED_WR_Byte                     
000028ed  SYSCFG_DL_MotorAFront_init           
00002979  SYSCFG_DL_MotorBFront_init           
00002a05  __aeabi_fmul                         
00002a05  __mulsf3                             
00002b15  SYSCFG_DL_initPower                  
00002b91  __TI_decompress_lzss                 
00002c0d  Task_Init                            
00002c85  Motor_Start                          
00002cf9  __gedf2                              
00002cf9  __gtdf2                              
00002d6d  OLED_ShowString                      
00002ddb  I2C_OLED_Clear                       
00002e45  __cmpdf2                             
00002e45  __eqdf2                              
00002e45  __ledf2                              
00002e45  __ltdf2                              
00002e45  __nedf2                              
00002f13  __aeabi_ldiv0                        
00002f15  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002f79  Key_Read                             
00002fdd  __aeabi_dcmpeq                       
00002ff1  __aeabi_dcmplt                       
00003005  __aeabi_dcmple                       
00003019  __aeabi_dcmpge                       
0000302d  __aeabi_dcmpgt                       
00003041  __aeabi_fcmpeq                       
00003055  __aeabi_fcmplt                       
00003069  __aeabi_fcmple                       
0000307d  __aeabi_fcmpge                       
00003091  __aeabi_fcmpgt                       
000030a5  I2C_OLED_i2c_sda_unlock              
00003105  SYSCFG_DL_I2C_OLED_init              
00003165  DL_I2C_fillControllerTXFIFO          
000031c5  Task_Tracker                         
00003221  frexp                                
00003221  frexpl                               
000032d9  __TI_ltoa                            
00003389  __aeabi_idiv                         
00003389  __aeabi_idivmod                      
00003435  SYSCFG_DL_SYSCTL_init                
0000357d  OLED_Printf                          
000035c9  __aeabi_d2iz                         
000035c9  __fixdfsi                            
00003613  Motor_GetSpeed                       
000036a5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000372d  __aeabi_d2uiz                        
0000372d  __fixunsdfsi                         
00003771  Interrupt_Init                       
000037b1  __aeabi_uidiv                        
000037b1  __aeabi_uidivmod                     
000037f1  __aeabi_f2d                          
000037f1  __extendsfdf2                        
00003831  atoi                                 
00003965  I2C_OLED_Set_Pos                     
000039a1  __aeabi_i2f                          
000039a1  __floatsisf                          
000039dd  __gesf2                              
000039dd  __gtsf2                              
00003a19  __TI_auto_init_nobinit_nopinit       
00003a55  __cmpsf2                             
00003a55  __eqsf2                              
00003a55  __lesf2                              
00003a55  __ltsf2                              
00003a55  __nesf2                              
00003a91  __muldsi3                            
00003b35  SYSCFG_DL_init                       
00003b69  _IQ24toF                             
00003bc9  _IQ24mpy                             
00003c21  __aeabi_i2d                          
00003c21  __floatsidf                          
00003c4d  vsprintf                             
00003c79  PID_Init                             
00003d45  SysTick_Increasment                  
00003d6d  _c_int00_noargs                      
00003dbb  DL_I2C_setClockConfig                
00003de1  __aeabi_ui2d                         
00003de1  __floatunsidf                        
00003e05  __aeabi_lmul                         
00003e05  __muldi3                             
00003e29  PID_SetParams                        
00003e4b  memccpy                              
00003e8d  Delay                                
00003ead  main                                 
00003ecd  __aeabi_llsl                         
00003ecd  __ashldi3                            
00004059  DL_Timer_setCaptCompUpdateMethod     
00004075  DL_Timer_setClockConfig              
00004319  DL_Timer_setCaptureCompareOutCtl     
000043b9  __TI_zero_init_nomemset              
00004471  __aeabi_uldivmod                     
00004499  TI_memcpy_small                      
000044ab  __TI_decompress_none                 
000044cd  DL_Timer_setCaptureCompareValue      
000044dd  SYSCFG_DL_SYSTICK_init               
000044ed  wcslen                               
000044fd  __aeabi_memset                       
000044fd  __aeabi_memset4                      
000044fd  __aeabi_memset8                      
00004519  TI_memset_small                      
00004529  Sys_GetTick                          
00004535  DL_Common_delayCycles                
0000456d  SysTick_Handler                      
00004575  __aeabi_errno_addr                   
0000457d  __aeabi_memcpy                       
0000457d  __aeabi_memcpy4                      
0000457d  __aeabi_memcpy8                      
00004585  abort                                
0000458b  ADC0_IRQHandler                      
0000458b  ADC1_IRQHandler                      
0000458b  AES_IRQHandler                       
0000458b  CANFD0_IRQHandler                    
0000458b  DAC0_IRQHandler                      
0000458b  DMA_IRQHandler                       
0000458b  Default_Handler                      
0000458b  GROUP0_IRQHandler                    
0000458b  HardFault_Handler                    
0000458b  I2C0_IRQHandler                      
0000458b  I2C1_IRQHandler                      
0000458b  NMI_Handler                          
0000458b  PendSV_Handler                       
0000458b  RTC_IRQHandler                       
0000458b  SPI0_IRQHandler                      
0000458b  SPI1_IRQHandler                      
0000458b  SVC_Handler                          
0000458b  TIMA0_IRQHandler                     
0000458b  TIMA1_IRQHandler                     
0000458b  TIMG0_IRQHandler                     
0000458b  TIMG12_IRQHandler                    
0000458b  TIMG6_IRQHandler                     
0000458b  TIMG7_IRQHandler                     
0000458b  TIMG8_IRQHandler                     
0000458b  UART0_IRQHandler                     
0000458b  UART1_IRQHandler                     
0000458b  UART2_IRQHandler                     
0000458b  UART3_IRQHandler                     
0000458e  C$$EXIT                              
0000458f  HOSTexit                             
00004593  Reset_Handler                        
000045a9  _system_pre_init                     
000045b0  asc2_1608                            
00004ba0  asc2_0806                            
00004dd0  __aeabi_ctype_table_                 
00004dd0  __aeabi_ctype_table_C                
00004ed1  _IQ6div_lookup                       
00004fe4  __TI_Handler_Table_Base              
00004ff0  __TI_Handler_Table_Limit             
00004ff8  __TI_CINIT_Base                      
00005008  __TI_CINIT_Limit                     
00005008  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  tick                                 
20200638  ExISR_Flag                           
2020063c  Motor_Font_Left                      
20200684  Motor_Font_Right                     
202006cc  Motor                                
202006d4  Data_Tracker_Input                   
202006db  Motor_Flag                           
202006dc  Data_MotorEncoder                    
202006e0  Data_Motor_TarSpeed                  
202006e4  Data_Tracker_Offset                  
202006e8  __aeabi_errno                        
202006ec  delayTick                            
202006f0  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[228 symbols]
