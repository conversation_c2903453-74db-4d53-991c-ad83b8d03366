******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 22:58:12 2025

OUTPUT FILE NAME:   <TI_CAR1.8 copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003ddd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005078  0001af88  R  X
  SRAM                  20200000   00008000  000008fb  00007705  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005078   00005078    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004560   00004560    r-x .text
  00004620    00004620    00000a00   00000a00    r-- .rodata
  00005020    00005020    00000058   00000058    r-- .cinit
20200000    20200000    000006fb   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    0000023c   00000000    rw- .bss
  2020063c    2020063c    000000bf   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004560     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000002a0     Tracker.o (.text.Tracker_Read)
                  00000d30    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000f50    000001dc            : _printfi.c.obj (.text._pconv_g)
                  0000112c    000001d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000012fc    000001b0     Task.o (.text.Task_Start)
                  000014ac    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000163e    00000002     Task.o (.text.Task_IdleFunction)
                  00001640    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000177c    00000134            : qsort.c.obj (.text.qsort)
                  000018b0    00000130     OLED.o (.text.OLED_ShowChar)
                  000019e0    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001b00    00000118     Task_App.o (.text.Task_Motor_PID)
                  00001c18    00000110     OLED.o (.text.OLED_Init)
                  00001d28    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001e34    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001f40    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002044    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002128    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002204    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000022dc    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000023b4    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002488    000000d4     Task_App.o (.text.Task_Key)
                  0000255c    000000c4     PID.o (.text.PID_SProsc)
                  00002620    000000b4     Task.o (.text.Task_Add)
                  000026d4    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002784    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002826    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002828    0000009c     Task_App.o (.text.Task_OLED)
                  000028c4    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  0000295c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  000029e8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002a74    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002b00    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002b84    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002c00    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002c7c    00000078     Task_App.o (.text.Task_Init)
                  00002cf4    00000074     Motor.o (.text.Motor_Start)
                  00002d68    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002ddc    0000006e     OLED.o (.text.OLED_ShowString)
                  00002e4a    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002eb4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002f1c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002f82    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002f84    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002fe8    00000064     Key_Led.o (.text.Key_Read)
                  0000304c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000030ae    00000002     --HOLE-- [fill = 0]
                  000030b0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003112    00000002     --HOLE-- [fill = 0]
                  00003114    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003174    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000031d4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003232    00000002     --HOLE-- [fill = 0]
                  00003234    0000005c     Task_App.o (.text.Task_Tracker)
                  00003290    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000032ec    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003348    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000033a0    00000058            : _printfi.c.obj (.text._pconv_f)
                  000033f8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000344e    00000002     --HOLE-- [fill = 0]
                  00003450    00000054     Motor.o (.text.CalculateDutyValue)
                  000034a4    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000034f8    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000354a    00000002     --HOLE-- [fill = 0]
                  0000354c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  0000359c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000035ec    0000004c     OLED.o (.text.OLED_Printf)
                  00003638    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003682    00000048     Motor.o (.text.Motor_GetSpeed)
                  000036ca    00000002     --HOLE-- [fill = 0]
                  000036cc    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00003714    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003758    00000044     Motor.o (.text.SetPWMValue)
                  0000379c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000037de    00000002     --HOLE-- [fill = 0]
                  000037e0    00000040     Interrupt.o (.text.Interrupt_Init)
                  00003820    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003860    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000038a0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000038e0    0000003e     Task.o (.text.Task_CMP)
                  0000391e    00000002     --HOLE-- [fill = 0]
                  00003920    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000395c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003998    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000039d4    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003a10    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003a4c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003a88    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003ac4    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003afe    00000002     --HOLE-- [fill = 0]
                  00003b00    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003b3a    00000002     --HOLE-- [fill = 0]
                  00003b3c    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003b70    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003ba4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003bd8    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003c08    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003c38    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003c64    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003c90    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003cbc    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003ce8    0000002a     PID.o (.text.PID_Init)
                  00003d12    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003d3a    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003d62    00000002     --HOLE-- [fill = 0]
                  00003d64    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003d8c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003db4    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003ddc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003e04    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003e2a    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003e50    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003e74    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003e98    00000022     PID.o (.text.PID_SetParams)
                  00003eba    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003edc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003efc    00000020     SysTick.o (.text.Delay)
                  00003f1c    00000020     main.o (.text.main)
                  00003f3c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003f5a    00000002     --HOLE-- [fill = 0]
                  00003f5c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003f78    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003f94    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00003fb0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003fcc    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00003fe8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004004    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004020    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0000403c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00004058    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00004074    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00004090    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000040ac    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000040c8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000040e4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004100    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004118    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004130    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004148    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00004160    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004178    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004190    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000041a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000041c0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000041d8    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  000041f0    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  00004208    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004220    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00004238    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004250    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00004268    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004280    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004298    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000042b0    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000042c8    00000018     OLED.o (.text.DL_I2C_reset)
                  000042e0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000042f8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004310    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004328    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004340    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004358    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004370    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004388    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000043a0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000043b8    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  000043d0    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000043e6    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000043fc    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00004412    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00004428    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000443e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00004452    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00004466    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  0000447a    00000002     --HOLE-- [fill = 0]
                  0000447c    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004490    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000044a4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000044b8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000044cc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000044e0    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000044f4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004508    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000451a    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000452c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000453c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000454c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000455c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  0000456c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000457a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004588    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004596    00000002     --HOLE-- [fill = 0]
                  00004598    0000000c     SysTick.o (.text.Sys_GetTick)
                  000045a4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000045ae    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000045b8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000045c8    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000045d2    0000000a            : vsprintf.c.obj (.text._outc)
                  000045dc    00000008     Interrupt.o (.text.SysTick_Handler)
                  000045e4    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000045ec    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000045f4    00000006     libc.a : exit.c.obj (.text:abort)
                  000045fa    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000045fe    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004602    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004606    00000002     --HOLE-- [fill = 0]
                  00004608    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004618    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000461c    00000004     --HOLE-- [fill = 0]

.cinit     0    00005020    00000058     
                  00005020    00000031     (.cinit..data.load) [load image, compression = lzss]
                  00005051    00000003     --HOLE-- [fill = 0]
                  00005054    0000000c     (__TI_handler_table)
                  00005060    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005068    00000010     (__TI_cinit_table)

.rodata    0    00004620    00000a00     
                  00004620    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004c10    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004e38    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00004e40    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004f41    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00004f82    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004f84    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004fac    00000014     Tracker.o (.rodata.str1.18388326728890721169.1)
                  00004fc0    00000012     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00004fd2    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004fe3    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004ff4    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00004ffc    00000008     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00005004    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000500a    00000005     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000500f    00000005     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00005014    00000004     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005018    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  0000501b    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  0000501e    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    0000023c     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000007     (.common:tick)
                  20200637    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200638    00000004     (.common:ExISR_Flag)

.data      0    2020063c    000000bf     UNINITIALIZED
                  2020063c    00000048     Motor.o (.data.Motor_Font_Left)
                  20200684    00000048     Motor.o (.data.Motor_Font_Right)
                  202006cc    00000008     Task_App.o (.data.Motor)
                  202006d4    00000007     Task_App.o (.data.Data_Tracker_Input)
                  202006db    00000001     Task_App.o (.data.Motor_Flag)
                  202006dc    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006e0    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006e4    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006e8    00000004     Tracker.o (.data.Tracker_Read.turn_start_time)
                  202006ec    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202006f0    00000004     SysTick.o (.data.delayTick)
                  202006f4    00000004     SysTick.o (.data.uwTick)
                  202006f8    00000001     Task.o (.data.Task_Num)
                  202006f9    00000001     Tracker.o (.data.Tracker_Read.is_turning)
                  202006fa    00000001     Tracker.o (.data.is_turning)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2418    64        320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2458    256       320    
                                                               
    .\APP\Src\
       Task_App.o                     904     46        29     
       Interrupt.o                    422     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         1326    46        33     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         676     0         241    
       Tracker.o                      738     20        13     
       Motor.o                        576     0         144    
       PID.o                          272     0         0      
       Key_Led.o                      122     0         0      
       SysTick.o                      84      0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4326    2092      406    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       132     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         886     0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2630    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       85        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17726   2835      2299   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005068 records: 2, size/record: 8, table size: 16
	.data: load addr=00005020, load size=00000031 bytes, run addr=2020063c, run size=000000bf bytes, compression=lzss
	.bss: load addr=00005060, load size=00000008 bytes, run addr=20200400, run size=0000023c bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005054 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000014ad     000045b8     000045b6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003ddd     00004608     00004602   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000045fb  ADC0_IRQHandler                      
000045fb  ADC1_IRQHandler                      
000045fb  AES_IRQHandler                       
000045fe  C$$EXIT                              
000045fb  CANFD0_IRQHandler                    
000045fb  DAC0_IRQHandler                      
000045a5  DL_Common_delayCycles                
000031d5  DL_I2C_fillControllerTXFIFO          
00003e2b  DL_I2C_setClockConfig                
00002129  DL_SYSCTL_configSYSPLL               
00002f85  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003715  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001f41  DL_Timer_initFourCCPWMMode           
000040c9  DL_Timer_setCaptCompUpdateMethod     
00004389  DL_Timer_setCaptureCompareOutCtl     
0000453d  DL_Timer_setCaptureCompareValue      
000040e5  DL_Timer_setClockConfig              
000045fb  DMA_IRQHandler                       
202006dc  Data_MotorEncoder                    
202006e0  Data_Motor_TarSpeed                  
202006d4  Data_Tracker_Input                   
202006e4  Data_Tracker_Offset                  
000045fb  Default_Handler                      
00003efd  Delay                                
20200638  ExISR_Flag                           
000045fb  GROUP0_IRQHandler                    
000026d5  GROUP1_IRQHandler                    
000045ff  HOSTexit                             
000045fb  HardFault_Handler                    
000045fb  I2C0_IRQHandler                      
000045fb  I2C1_IRQHandler                      
00002e4b  I2C_OLED_Clear                       
000039d5  I2C_OLED_Set_Pos                     
000028c5  I2C_OLED_WR_Byte                     
00003115  I2C_OLED_i2c_sda_unlock              
000037e1  Interrupt_Init                       
00002fe9  Key_Read                             
202006cc  Motor                                
202006db  Motor_Flag                           
2020063c  Motor_Font_Left                      
20200684  Motor_Font_Right                     
00003683  Motor_GetSpeed                       
000023b5  Motor_SetDuty                        
00002cf5  Motor_Start                          
000045fb  NMI_Handler                          
00001c19  OLED_Init                            
000035ed  OLED_Printf                          
000018b1  OLED_ShowChar                        
00002ddd  OLED_ShowString                      
00003ce9  PID_Init                             
0000255d  PID_SProsc                           
00003e99  PID_SetParams                        
000045fb  PendSV_Handler                       
000045fb  RTC_IRQHandler                       
00004603  Reset_Handler                        
000045fb  SPI0_IRQHandler                      
000045fb  SPI1_IRQHandler                      
000045fb  SVC_Handler                          
0000112d  SYSCFG_DL_GPIO_init                  
00003175  SYSCFG_DL_I2C_OLED_init              
0000295d  SYSCFG_DL_MotorAFront_init           
000029e9  SYSCFG_DL_MotorBFront_init           
000034a5  SYSCFG_DL_SYSCTL_init                
0000454d  SYSCFG_DL_SYSTICK_init               
00003ba5  SYSCFG_DL_init                       
00002b85  SYSCFG_DL_initPower                  
000045dd  SysTick_Handler                      
00003db5  SysTick_Increasment                  
00004599  Sys_GetTick                          
000045fb  TIMA0_IRQHandler                     
000045fb  TIMA1_IRQHandler                     
000045fb  TIMG0_IRQHandler                     
000045fb  TIMG12_IRQHandler                    
000045fb  TIMG6_IRQHandler                     
000045fb  TIMG7_IRQHandler                     
000045fb  TIMG8_IRQHandler                     
00004509  TI_memcpy_small                      
00004589  TI_memset_small                      
00002621  Task_Add                             
0000163f  Task_IdleFunction                    
00002c7d  Task_Init                            
00002489  Task_Key                             
00001b01  Task_Motor_PID                       
00002829  Task_OLED                            
000012fd  Task_Start                           
00003235  Task_Tracker                         
00000a91  Tracker_Read                         
000045fb  UART0_IRQHandler                     
000045fb  UART1_IRQHandler                     
000045fb  UART2_IRQHandler                     
000045fb  UART3_IRQHandler                     
00001d29  _IQ24div                             
00003c39  _IQ24mpy                             
00003bd9  _IQ24toF                             
00004f41  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005068  __TI_CINIT_Base                      
00005078  __TI_CINIT_Limit                     
00005078  __TI_CINIT_Warm                      
00005054  __TI_Handler_Table_Base              
00005060  __TI_Handler_Table_Limit             
00003a89  __TI_auto_init_nobinit_nopinit       
00002c01  __TI_decompress_lzss                 
0000451b  __TI_decompress_none                 
00003349  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004429  __TI_zero_init_nomemset              
000014b7  __adddf3                             
000022e7  __addsf3                             
00004e40  __aeabi_ctype_table_                 
00004e40  __aeabi_ctype_table_C                
00003639  __aeabi_d2iz                         
0000379d  __aeabi_d2uiz                        
000014b7  __aeabi_dadd                         
0000304d  __aeabi_dcmpeq                       
00003089  __aeabi_dcmpge                       
0000309d  __aeabi_dcmpgt                       
00003075  __aeabi_dcmple                       
00003061  __aeabi_dcmplt                       
00001e35  __aeabi_ddiv                         
00002045  __aeabi_dmul                         
000014ad  __aeabi_dsub                         
202006ec  __aeabi_errno                        
000045e5  __aeabi_errno_addr                   
00003861  __aeabi_f2d                          
000022e7  __aeabi_fadd                         
000030b1  __aeabi_fcmpeq                       
000030ed  __aeabi_fcmpge                       
00003101  __aeabi_fcmpgt                       
000030d9  __aeabi_fcmple                       
000030c5  __aeabi_fcmplt                       
00002a75  __aeabi_fmul                         
000022dd  __aeabi_fsub                         
00003c91  __aeabi_i2d                          
00003a11  __aeabi_i2f                          
000033f9  __aeabi_idiv                         
00002827  __aeabi_idiv0                        
000033f9  __aeabi_idivmod                      
00002f83  __aeabi_ldiv0                        
00003f3d  __aeabi_llsl                         
00003e75  __aeabi_lmul                         
000045ed  __aeabi_memcpy                       
000045ed  __aeabi_memcpy4                      
000045ed  __aeabi_memcpy8                      
0000456d  __aeabi_memset                       
0000456d  __aeabi_memset4                      
0000456d  __aeabi_memset8                      
00003e51  __aeabi_ui2d                         
00003821  __aeabi_uidiv                        
00003821  __aeabi_uidivmod                     
000044e1  __aeabi_uldivmod                     
00003f3d  __ashldi3                            
ffffffff  __binit__                            
00002eb5  __cmpdf2                             
00003ac5  __cmpsf2                             
00001e35  __divdf3                             
00002eb5  __eqdf2                              
00003ac5  __eqsf2                              
00003861  __extendsfdf2                        
00003639  __fixdfsi                            
0000379d  __fixunsdfsi                         
00003c91  __floatsidf                          
00003a11  __floatsisf                          
00003e51  __floatunsidf                        
00002d69  __gedf2                              
00003a4d  __gesf2                              
00002d69  __gtdf2                              
00003a4d  __gtsf2                              
00002eb5  __ledf2                              
00003ac5  __lesf2                              
00002eb5  __ltdf2                              
00003ac5  __ltsf2                              
UNDEFED   __mpu_init                           
00002045  __muldf3                             
00003e75  __muldi3                             
00003b01  __muldsi3                            
00002a75  __mulsf3                             
00002eb5  __nedf2                              
00003ac5  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000014ad  __subdf3                             
000022dd  __subsf3                             
00002785  __udivmoddi4                         
00003ddd  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00004619  _system_pre_init                     
000045f5  abort                                
00004c10  asc2_0806                            
00004620  asc2_1608                            
000038a1  atoi                                 
ffffffff  binit                                
202006f0  delayTick                            
00003291  frexp                                
00003291  frexpl                               
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
202006fa  is_turning                           
00002205  ldexp                                
00002205  ldexpl                               
00003f1d  main                                 
00003ebb  memccpy                              
0000177d  qsort                                
00002205  scalbn                               
00002205  scalbnl                              
20200630  tick                                 
202006f4  uwTick                               
00003cbd  vsprintf                             
0000455d  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  Tracker_Read                         
0000112d  SYSCFG_DL_GPIO_init                  
000012fd  Task_Start                           
000014ad  __aeabi_dsub                         
000014ad  __subdf3                             
000014b7  __adddf3                             
000014b7  __aeabi_dadd                         
0000163f  Task_IdleFunction                    
0000177d  qsort                                
000018b1  OLED_ShowChar                        
00001b01  Task_Motor_PID                       
00001c19  OLED_Init                            
00001d29  _IQ24div                             
00001e35  __aeabi_ddiv                         
00001e35  __divdf3                             
00001f41  DL_Timer_initFourCCPWMMode           
00002045  __aeabi_dmul                         
00002045  __muldf3                             
00002129  DL_SYSCTL_configSYSPLL               
00002205  ldexp                                
00002205  ldexpl                               
00002205  scalbn                               
00002205  scalbnl                              
000022dd  __aeabi_fsub                         
000022dd  __subsf3                             
000022e7  __addsf3                             
000022e7  __aeabi_fadd                         
000023b5  Motor_SetDuty                        
00002489  Task_Key                             
0000255d  PID_SProsc                           
00002621  Task_Add                             
000026d5  GROUP1_IRQHandler                    
00002785  __udivmoddi4                         
00002827  __aeabi_idiv0                        
00002829  Task_OLED                            
000028c5  I2C_OLED_WR_Byte                     
0000295d  SYSCFG_DL_MotorAFront_init           
000029e9  SYSCFG_DL_MotorBFront_init           
00002a75  __aeabi_fmul                         
00002a75  __mulsf3                             
00002b85  SYSCFG_DL_initPower                  
00002c01  __TI_decompress_lzss                 
00002c7d  Task_Init                            
00002cf5  Motor_Start                          
00002d69  __gedf2                              
00002d69  __gtdf2                              
00002ddd  OLED_ShowString                      
00002e4b  I2C_OLED_Clear                       
00002eb5  __cmpdf2                             
00002eb5  __eqdf2                              
00002eb5  __ledf2                              
00002eb5  __ltdf2                              
00002eb5  __nedf2                              
00002f83  __aeabi_ldiv0                        
00002f85  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002fe9  Key_Read                             
0000304d  __aeabi_dcmpeq                       
00003061  __aeabi_dcmplt                       
00003075  __aeabi_dcmple                       
00003089  __aeabi_dcmpge                       
0000309d  __aeabi_dcmpgt                       
000030b1  __aeabi_fcmpeq                       
000030c5  __aeabi_fcmplt                       
000030d9  __aeabi_fcmple                       
000030ed  __aeabi_fcmpge                       
00003101  __aeabi_fcmpgt                       
00003115  I2C_OLED_i2c_sda_unlock              
00003175  SYSCFG_DL_I2C_OLED_init              
000031d5  DL_I2C_fillControllerTXFIFO          
00003235  Task_Tracker                         
00003291  frexp                                
00003291  frexpl                               
00003349  __TI_ltoa                            
000033f9  __aeabi_idiv                         
000033f9  __aeabi_idivmod                      
000034a5  SYSCFG_DL_SYSCTL_init                
000035ed  OLED_Printf                          
00003639  __aeabi_d2iz                         
00003639  __fixdfsi                            
00003683  Motor_GetSpeed                       
00003715  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000379d  __aeabi_d2uiz                        
0000379d  __fixunsdfsi                         
000037e1  Interrupt_Init                       
00003821  __aeabi_uidiv                        
00003821  __aeabi_uidivmod                     
00003861  __aeabi_f2d                          
00003861  __extendsfdf2                        
000038a1  atoi                                 
000039d5  I2C_OLED_Set_Pos                     
00003a11  __aeabi_i2f                          
00003a11  __floatsisf                          
00003a4d  __gesf2                              
00003a4d  __gtsf2                              
00003a89  __TI_auto_init_nobinit_nopinit       
00003ac5  __cmpsf2                             
00003ac5  __eqsf2                              
00003ac5  __lesf2                              
00003ac5  __ltsf2                              
00003ac5  __nesf2                              
00003b01  __muldsi3                            
00003ba5  SYSCFG_DL_init                       
00003bd9  _IQ24toF                             
00003c39  _IQ24mpy                             
00003c91  __aeabi_i2d                          
00003c91  __floatsidf                          
00003cbd  vsprintf                             
00003ce9  PID_Init                             
00003db5  SysTick_Increasment                  
00003ddd  _c_int00_noargs                      
00003e2b  DL_I2C_setClockConfig                
00003e51  __aeabi_ui2d                         
00003e51  __floatunsidf                        
00003e75  __aeabi_lmul                         
00003e75  __muldi3                             
00003e99  PID_SetParams                        
00003ebb  memccpy                              
00003efd  Delay                                
00003f1d  main                                 
00003f3d  __aeabi_llsl                         
00003f3d  __ashldi3                            
000040c9  DL_Timer_setCaptCompUpdateMethod     
000040e5  DL_Timer_setClockConfig              
00004389  DL_Timer_setCaptureCompareOutCtl     
00004429  __TI_zero_init_nomemset              
000044e1  __aeabi_uldivmod                     
00004509  TI_memcpy_small                      
0000451b  __TI_decompress_none                 
0000453d  DL_Timer_setCaptureCompareValue      
0000454d  SYSCFG_DL_SYSTICK_init               
0000455d  wcslen                               
0000456d  __aeabi_memset                       
0000456d  __aeabi_memset4                      
0000456d  __aeabi_memset8                      
00004589  TI_memset_small                      
00004599  Sys_GetTick                          
000045a5  DL_Common_delayCycles                
000045dd  SysTick_Handler                      
000045e5  __aeabi_errno_addr                   
000045ed  __aeabi_memcpy                       
000045ed  __aeabi_memcpy4                      
000045ed  __aeabi_memcpy8                      
000045f5  abort                                
000045fb  ADC0_IRQHandler                      
000045fb  ADC1_IRQHandler                      
000045fb  AES_IRQHandler                       
000045fb  CANFD0_IRQHandler                    
000045fb  DAC0_IRQHandler                      
000045fb  DMA_IRQHandler                       
000045fb  Default_Handler                      
000045fb  GROUP0_IRQHandler                    
000045fb  HardFault_Handler                    
000045fb  I2C0_IRQHandler                      
000045fb  I2C1_IRQHandler                      
000045fb  NMI_Handler                          
000045fb  PendSV_Handler                       
000045fb  RTC_IRQHandler                       
000045fb  SPI0_IRQHandler                      
000045fb  SPI1_IRQHandler                      
000045fb  SVC_Handler                          
000045fb  TIMA0_IRQHandler                     
000045fb  TIMA1_IRQHandler                     
000045fb  TIMG0_IRQHandler                     
000045fb  TIMG12_IRQHandler                    
000045fb  TIMG6_IRQHandler                     
000045fb  TIMG7_IRQHandler                     
000045fb  TIMG8_IRQHandler                     
000045fb  UART0_IRQHandler                     
000045fb  UART1_IRQHandler                     
000045fb  UART2_IRQHandler                     
000045fb  UART3_IRQHandler                     
000045fe  C$$EXIT                              
000045ff  HOSTexit                             
00004603  Reset_Handler                        
00004619  _system_pre_init                     
00004620  asc2_1608                            
00004c10  asc2_0806                            
00004e40  __aeabi_ctype_table_                 
00004e40  __aeabi_ctype_table_C                
00004f41  _IQ6div_lookup                       
00005054  __TI_Handler_Table_Base              
00005060  __TI_Handler_Table_Limit             
00005068  __TI_CINIT_Base                      
00005078  __TI_CINIT_Limit                     
00005078  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  tick                                 
20200638  ExISR_Flag                           
2020063c  Motor_Font_Left                      
20200684  Motor_Font_Right                     
202006cc  Motor                                
202006d4  Data_Tracker_Input                   
202006db  Motor_Flag                           
202006dc  Data_MotorEncoder                    
202006e0  Data_Motor_TarSpeed                  
202006e4  Data_Tracker_Offset                  
202006ec  __aeabi_errno                        
202006f0  delayTick                            
202006f4  uwTick                               
202006fa  is_turning                           
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[229 symbols]
