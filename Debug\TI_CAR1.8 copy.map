******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 20:36:25 2025

OUTPUT FILE NAME:   <TI_CAR1.8 copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003d99


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004ff8  0001b008  R  X
  SRAM                  20200000   00008000  000008f5  0000770b  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004ff8   00004ff8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000044e0   000044e0    r-x .text
  000045a0    000045a0    00000a00   00000a00    r-- .rodata
  00004fa0    00004fa0    00000058   00000058    r-- .cinit
20200000    20200000    000006f5   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    0000023c   00000000    rw- .bss
  2020063c    2020063c    000000b9   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000044e0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    0000022c     Tracker.o (.text.Tracker_Read)
                  00000cbc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000edc    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000010b8    000001d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001288    000001b0     Task.o (.text.Task_Start)
                  00001438    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000015ca    00000002     Task.o (.text.Task_IdleFunction)
                  000015cc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001708    00000134            : qsort.c.obj (.text.qsort)
                  0000183c    00000130     OLED.o (.text.OLED_ShowChar)
                  0000196c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001a8c    00000110     OLED.o (.text.OLED_Init)
                  00001b9c    00000110     Task_App.o (.text.Task_Motor_PID)
                  00001cac    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001db8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001ec4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001fc8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000020ac    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002188    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002260    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002338    000000d4     Motor.o (.text.Motor_SetDuty)
                  0000240c    000000d4     Task_App.o (.text.Task_Key)
                  000024e0    000000c4     PID.o (.text.PID_SProsc)
                  000025a4    000000b4     Task.o (.text.Task_Add)
                  00002658    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002708    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000027aa    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000027ac    0000009c     Task_App.o (.text.Task_OLED)
                  00002848    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000028e0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  0000296c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  000029f8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002a84    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002b08    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002b84    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002c00    00000078     Task_App.o (.text.Task_Init)
                  00002c78    00000074     Motor.o (.text.Motor_Start)
                  00002cec    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002d60    0000006e     OLED.o (.text.OLED_ShowString)
                  00002dce    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002e38    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002ea0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002f06    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002f08    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002f6c    00000064     Key_Led.o (.text.Key_Read)
                  00002fd0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003032    00000002     --HOLE-- [fill = 0]
                  00003034    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003096    00000002     --HOLE-- [fill = 0]
                  00003098    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  000030f8    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003158    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000031b6    00000002     --HOLE-- [fill = 0]
                  000031b8    0000005c     Task_App.o (.text.Task_Tracker)
                  00003214    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003270    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000032cc    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003324    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000337c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000033d2    00000002     --HOLE-- [fill = 0]
                  000033d4    00000054     Motor.o (.text.CalculateDutyValue)
                  00003428    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000347c    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000034ce    00000002     --HOLE-- [fill = 0]
                  000034d0    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00003520    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003570    0000004c     OLED.o (.text.OLED_Printf)
                  000035bc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003606    00000048     Motor.o (.text.Motor_GetSpeed)
                  0000364e    00000002     --HOLE-- [fill = 0]
                  00003650    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00003698    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000036dc    00000044     Motor.o (.text.SetPWMValue)
                  00003720    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00003762    00000002     --HOLE-- [fill = 0]
                  00003764    00000040     Interrupt.o (.text.Interrupt_Init)
                  000037a4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000037e4    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003824    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003864    0000003e     Task.o (.text.Task_CMP)
                  000038a2    00000002     --HOLE-- [fill = 0]
                  000038a4    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000038e0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000391c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003958    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003994    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000039d0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003a0c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003a48    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003a82    00000002     --HOLE-- [fill = 0]
                  00003a84    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003abe    00000002     --HOLE-- [fill = 0]
                  00003ac0    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003af8    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003b2c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003b60    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003b94    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003bc4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003bf4    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003c20    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003c4c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003c78    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003ca4    0000002a     PID.o (.text.PID_Init)
                  00003cce    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003cf6    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003d1e    00000002     --HOLE-- [fill = 0]
                  00003d20    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003d48    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003d70    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003d98    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003dc0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003de6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003e0c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003e30    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003e54    00000022     PID.o (.text.PID_SetParams)
                  00003e76    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003e98    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003eb8    00000020     SysTick.o (.text.Delay)
                  00003ed8    00000020     main.o (.text.main)
                  00003ef8    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003f16    00000002     --HOLE-- [fill = 0]
                  00003f18    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003f34    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003f50    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00003f6c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003f88    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00003fa4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003fc0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003fdc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00003ff8    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00004014    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00004030    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  0000404c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004068    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004084    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000040a0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000040bc    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000040d4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000040ec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004104    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000411c    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004134    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000414c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004164    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000417c    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004194    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  000041ac    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000041c4    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000041dc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000041f4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000420c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004224    00000018     OLED.o (.text.DL_I2C_enablePower)
                  0000423c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004254    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  0000426c    00000018     OLED.o (.text.DL_I2C_reset)
                  00004284    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000429c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000042b4    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000042cc    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000042e4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000042fc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004314    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000432c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004344    00000018     Motor.o (.text.DL_Timer_startCounter)
                  0000435c    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004374    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  0000438a    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000043a0    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000043b6    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  000043cc    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000043e2    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000043f6    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  0000440a    00000002     --HOLE-- [fill = 0]
                  0000440c    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004420    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004434    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004448    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000445c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004470    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004484    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004498    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  000044aa    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000044bc    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000044cc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000044dc    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000044ec    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000044fc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000450a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004518    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004526    00000002     --HOLE-- [fill = 0]
                  00004528    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004534    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000453e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004548    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004558    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004562    0000000a            : vsprintf.c.obj (.text._outc)
                  0000456c    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004574    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000457c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004584    00000006     libc.a : exit.c.obj (.text:abort)
                  0000458a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000458e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004592    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004596    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000459a    00000006     --HOLE-- [fill = 0]

.cinit     0    00004fa0    00000058     
                  00004fa0    00000031     (.cinit..data.load) [load image, compression = lzss]
                  00004fd1    00000003     --HOLE-- [fill = 0]
                  00004fd4    0000000c     (__TI_handler_table)
                  00004fe0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004fe8    00000010     (__TI_cinit_table)

.rodata    0    000045a0    00000a00     
                  000045a0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004b90    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004db8    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00004dc0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004ec1    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00004f02    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004f04    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004f2c    00000014     Tracker.o (.rodata.str1.18388326728890721169.1)
                  00004f40    00000012     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00004f52    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004f63    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004f74    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00004f7c    00000008     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00004f84    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00004f8a    00000005     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00004f8f    00000005     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00004f94    00000004     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00004f98    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00004f9b    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00004f9e    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    0000023c     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000007     (.common:tick)
                  20200637    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200638    00000004     (.common:ExISR_Flag)

.data      0    2020063c    000000b9     UNINITIALIZED
                  2020063c    00000048     Motor.o (.data.Motor_Font_Left)
                  20200684    00000048     Motor.o (.data.Motor_Font_Right)
                  202006cc    00000008     Task_App.o (.data.Motor)
                  202006d4    00000007     Task_App.o (.data.Data_Tracker_Input)
                  202006db    00000001     Task_App.o (.data.Motor_Flag)
                  202006dc    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006e0    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006e4    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006e8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202006ec    00000004     SysTick.o (.data.delayTick)
                  202006f0    00000004     SysTick.o (.data.uwTick)
                  202006f4    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2418    64        320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2458    256       320    
                                                               
    .\APP\Src\
       Task_App.o                     896     46        29     
       Interrupt.o                    422     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         1318    46        33     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         676     0         241    
       Motor.o                        576     0         144    
       Tracker.o                      578     20        7      
       PID.o                          272     0         0      
       Key_Led.o                      122     0         0      
       SysTick.o                      84      0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4166    2092      400    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       132     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         886     0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5720    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2686    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       85        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17598   2835      2293   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004fe8 records: 2, size/record: 8, table size: 16
	.data: load addr=00004fa0, load size=00000031 bytes, run addr=2020063c, run size=000000b9 bytes, compression=lzss
	.bss: load addr=00004fe0, load size=00000008 bytes, run addr=20200400, run size=0000023c bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004fd4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001439     00004548     00004546   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000458b  ADC0_IRQHandler                      
0000458b  ADC1_IRQHandler                      
0000458b  AES_IRQHandler                       
0000458e  C$$EXIT                              
0000458b  CANFD0_IRQHandler                    
0000458b  DAC0_IRQHandler                      
00004535  DL_Common_delayCycles                
00003159  DL_I2C_fillControllerTXFIFO          
00003de7  DL_I2C_setClockConfig                
000020ad  DL_SYSCTL_configSYSPLL               
00002f09  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003699  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001ec5  DL_Timer_initFourCCPWMMode           
00004085  DL_Timer_setCaptCompUpdateMethod     
0000432d  DL_Timer_setCaptureCompareOutCtl     
000044cd  DL_Timer_setCaptureCompareValue      
000040a1  DL_Timer_setClockConfig              
0000458b  DMA_IRQHandler                       
202006dc  Data_MotorEncoder                    
202006e0  Data_Motor_TarSpeed                  
202006d4  Data_Tracker_Input                   
202006e4  Data_Tracker_Offset                  
0000458b  Default_Handler                      
00003eb9  Delay                                
20200638  ExISR_Flag                           
0000458b  GROUP0_IRQHandler                    
00002659  GROUP1_IRQHandler                    
0000458f  HOSTexit                             
0000458b  HardFault_Handler                    
0000458b  I2C0_IRQHandler                      
0000458b  I2C1_IRQHandler                      
00002dcf  I2C_OLED_Clear                       
00003959  I2C_OLED_Set_Pos                     
00002849  I2C_OLED_WR_Byte                     
00003099  I2C_OLED_i2c_sda_unlock              
00003765  Interrupt_Init                       
00002f6d  Key_Read                             
202006cc  Motor                                
202006db  Motor_Flag                           
2020063c  Motor_Font_Left                      
20200684  Motor_Font_Right                     
00003607  Motor_GetSpeed                       
00002339  Motor_SetDuty                        
00002c79  Motor_Start                          
0000458b  NMI_Handler                          
00001a8d  OLED_Init                            
00003571  OLED_Printf                          
0000183d  OLED_ShowChar                        
00002d61  OLED_ShowString                      
00003ca5  PID_Init                             
000024e1  PID_SProsc                           
00003e55  PID_SetParams                        
0000458b  PendSV_Handler                       
0000458b  RTC_IRQHandler                       
00004593  Reset_Handler                        
0000458b  SPI0_IRQHandler                      
0000458b  SPI1_IRQHandler                      
0000458b  SVC_Handler                          
000010b9  SYSCFG_DL_GPIO_init                  
000030f9  SYSCFG_DL_I2C_OLED_init              
000028e1  SYSCFG_DL_MotorAFront_init           
0000296d  SYSCFG_DL_MotorBFront_init           
00003429  SYSCFG_DL_SYSCTL_init                
000044dd  SYSCFG_DL_SYSTICK_init               
00003b61  SYSCFG_DL_init                       
00002b09  SYSCFG_DL_initPower                  
0000456d  SysTick_Handler                      
00003d71  SysTick_Increasment                  
00004529  Sys_GetTick                          
0000458b  TIMA0_IRQHandler                     
0000458b  TIMA1_IRQHandler                     
0000458b  TIMG0_IRQHandler                     
0000458b  TIMG12_IRQHandler                    
0000458b  TIMG6_IRQHandler                     
0000458b  TIMG7_IRQHandler                     
0000458b  TIMG8_IRQHandler                     
00004499  TI_memcpy_small                      
00004519  TI_memset_small                      
000025a5  Task_Add                             
000015cb  Task_IdleFunction                    
00002c01  Task_Init                            
0000240d  Task_Key                             
00001b9d  Task_Motor_PID                       
000027ad  Task_OLED                            
00001289  Task_Start                           
000031b9  Task_Tracker                         
00000a91  Tracker_Read                         
0000458b  UART0_IRQHandler                     
0000458b  UART1_IRQHandler                     
0000458b  UART2_IRQHandler                     
0000458b  UART3_IRQHandler                     
00001cad  _IQ24div                             
00003bf5  _IQ24mpy                             
00003b95  _IQ24toF                             
00004ec1  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004fe8  __TI_CINIT_Base                      
00004ff8  __TI_CINIT_Limit                     
00004ff8  __TI_CINIT_Warm                      
00004fd4  __TI_Handler_Table_Base              
00004fe0  __TI_Handler_Table_Limit             
00003a0d  __TI_auto_init_nobinit_nopinit       
00002b85  __TI_decompress_lzss                 
000044ab  __TI_decompress_none                 
000032cd  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000043cd  __TI_zero_init_nomemset              
00001443  __adddf3                             
0000226b  __addsf3                             
00004dc0  __aeabi_ctype_table_                 
00004dc0  __aeabi_ctype_table_C                
000035bd  __aeabi_d2iz                         
00003721  __aeabi_d2uiz                        
00001443  __aeabi_dadd                         
00002fd1  __aeabi_dcmpeq                       
0000300d  __aeabi_dcmpge                       
00003021  __aeabi_dcmpgt                       
00002ff9  __aeabi_dcmple                       
00002fe5  __aeabi_dcmplt                       
00001db9  __aeabi_ddiv                         
00001fc9  __aeabi_dmul                         
00001439  __aeabi_dsub                         
202006e8  __aeabi_errno                        
00004575  __aeabi_errno_addr                   
000037e5  __aeabi_f2d                          
00003ac1  __aeabi_f2iz                         
0000226b  __aeabi_fadd                         
00003035  __aeabi_fcmpeq                       
00003071  __aeabi_fcmpge                       
00003085  __aeabi_fcmpgt                       
0000305d  __aeabi_fcmple                       
00003049  __aeabi_fcmplt                       
000029f9  __aeabi_fmul                         
00002261  __aeabi_fsub                         
00003c4d  __aeabi_i2d                          
00003995  __aeabi_i2f                          
0000337d  __aeabi_idiv                         
000027ab  __aeabi_idiv0                        
0000337d  __aeabi_idivmod                      
00002f07  __aeabi_ldiv0                        
00003ef9  __aeabi_llsl                         
00003e31  __aeabi_lmul                         
0000457d  __aeabi_memcpy                       
0000457d  __aeabi_memcpy4                      
0000457d  __aeabi_memcpy8                      
000044fd  __aeabi_memset                       
000044fd  __aeabi_memset4                      
000044fd  __aeabi_memset8                      
00003e0d  __aeabi_ui2d                         
000037a5  __aeabi_uidiv                        
000037a5  __aeabi_uidivmod                     
00004471  __aeabi_uldivmod                     
00003ef9  __ashldi3                            
ffffffff  __binit__                            
00002e39  __cmpdf2                             
00003a49  __cmpsf2                             
00001db9  __divdf3                             
00002e39  __eqdf2                              
00003a49  __eqsf2                              
000037e5  __extendsfdf2                        
000035bd  __fixdfsi                            
00003ac1  __fixsfsi                            
00003721  __fixunsdfsi                         
00003c4d  __floatsidf                          
00003995  __floatsisf                          
00003e0d  __floatunsidf                        
00002ced  __gedf2                              
000039d1  __gesf2                              
00002ced  __gtdf2                              
000039d1  __gtsf2                              
00002e39  __ledf2                              
00003a49  __lesf2                              
00002e39  __ltdf2                              
00003a49  __ltsf2                              
UNDEFED   __mpu_init                           
00001fc9  __muldf3                             
00003e31  __muldi3                             
00003a85  __muldsi3                            
000029f9  __mulsf3                             
00002e39  __nedf2                              
00003a49  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001439  __subdf3                             
00002261  __subsf3                             
00002709  __udivmoddi4                         
00003d99  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00004597  _system_pre_init                     
00004585  abort                                
00004b90  asc2_0806                            
000045a0  asc2_1608                            
00003825  atoi                                 
ffffffff  binit                                
202006ec  delayTick                            
00003215  frexp                                
00003215  frexpl                               
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
00002189  ldexp                                
00002189  ldexpl                               
00003ed9  main                                 
00003e77  memccpy                              
00001709  qsort                                
00002189  scalbn                               
00002189  scalbnl                              
20200630  tick                                 
202006f0  uwTick                               
00003c79  vsprintf                             
000044ed  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  Tracker_Read                         
000010b9  SYSCFG_DL_GPIO_init                  
00001289  Task_Start                           
00001439  __aeabi_dsub                         
00001439  __subdf3                             
00001443  __adddf3                             
00001443  __aeabi_dadd                         
000015cb  Task_IdleFunction                    
00001709  qsort                                
0000183d  OLED_ShowChar                        
00001a8d  OLED_Init                            
00001b9d  Task_Motor_PID                       
00001cad  _IQ24div                             
00001db9  __aeabi_ddiv                         
00001db9  __divdf3                             
00001ec5  DL_Timer_initFourCCPWMMode           
00001fc9  __aeabi_dmul                         
00001fc9  __muldf3                             
000020ad  DL_SYSCTL_configSYSPLL               
00002189  ldexp                                
00002189  ldexpl                               
00002189  scalbn                               
00002189  scalbnl                              
00002261  __aeabi_fsub                         
00002261  __subsf3                             
0000226b  __addsf3                             
0000226b  __aeabi_fadd                         
00002339  Motor_SetDuty                        
0000240d  Task_Key                             
000024e1  PID_SProsc                           
000025a5  Task_Add                             
00002659  GROUP1_IRQHandler                    
00002709  __udivmoddi4                         
000027ab  __aeabi_idiv0                        
000027ad  Task_OLED                            
00002849  I2C_OLED_WR_Byte                     
000028e1  SYSCFG_DL_MotorAFront_init           
0000296d  SYSCFG_DL_MotorBFront_init           
000029f9  __aeabi_fmul                         
000029f9  __mulsf3                             
00002b09  SYSCFG_DL_initPower                  
00002b85  __TI_decompress_lzss                 
00002c01  Task_Init                            
00002c79  Motor_Start                          
00002ced  __gedf2                              
00002ced  __gtdf2                              
00002d61  OLED_ShowString                      
00002dcf  I2C_OLED_Clear                       
00002e39  __cmpdf2                             
00002e39  __eqdf2                              
00002e39  __ledf2                              
00002e39  __ltdf2                              
00002e39  __nedf2                              
00002f07  __aeabi_ldiv0                        
00002f09  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002f6d  Key_Read                             
00002fd1  __aeabi_dcmpeq                       
00002fe5  __aeabi_dcmplt                       
00002ff9  __aeabi_dcmple                       
0000300d  __aeabi_dcmpge                       
00003021  __aeabi_dcmpgt                       
00003035  __aeabi_fcmpeq                       
00003049  __aeabi_fcmplt                       
0000305d  __aeabi_fcmple                       
00003071  __aeabi_fcmpge                       
00003085  __aeabi_fcmpgt                       
00003099  I2C_OLED_i2c_sda_unlock              
000030f9  SYSCFG_DL_I2C_OLED_init              
00003159  DL_I2C_fillControllerTXFIFO          
000031b9  Task_Tracker                         
00003215  frexp                                
00003215  frexpl                               
000032cd  __TI_ltoa                            
0000337d  __aeabi_idiv                         
0000337d  __aeabi_idivmod                      
00003429  SYSCFG_DL_SYSCTL_init                
00003571  OLED_Printf                          
000035bd  __aeabi_d2iz                         
000035bd  __fixdfsi                            
00003607  Motor_GetSpeed                       
00003699  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003721  __aeabi_d2uiz                        
00003721  __fixunsdfsi                         
00003765  Interrupt_Init                       
000037a5  __aeabi_uidiv                        
000037a5  __aeabi_uidivmod                     
000037e5  __aeabi_f2d                          
000037e5  __extendsfdf2                        
00003825  atoi                                 
00003959  I2C_OLED_Set_Pos                     
00003995  __aeabi_i2f                          
00003995  __floatsisf                          
000039d1  __gesf2                              
000039d1  __gtsf2                              
00003a0d  __TI_auto_init_nobinit_nopinit       
00003a49  __cmpsf2                             
00003a49  __eqsf2                              
00003a49  __lesf2                              
00003a49  __ltsf2                              
00003a49  __nesf2                              
00003a85  __muldsi3                            
00003ac1  __aeabi_f2iz                         
00003ac1  __fixsfsi                            
00003b61  SYSCFG_DL_init                       
00003b95  _IQ24toF                             
00003bf5  _IQ24mpy                             
00003c4d  __aeabi_i2d                          
00003c4d  __floatsidf                          
00003c79  vsprintf                             
00003ca5  PID_Init                             
00003d71  SysTick_Increasment                  
00003d99  _c_int00_noargs                      
00003de7  DL_I2C_setClockConfig                
00003e0d  __aeabi_ui2d                         
00003e0d  __floatunsidf                        
00003e31  __aeabi_lmul                         
00003e31  __muldi3                             
00003e55  PID_SetParams                        
00003e77  memccpy                              
00003eb9  Delay                                
00003ed9  main                                 
00003ef9  __aeabi_llsl                         
00003ef9  __ashldi3                            
00004085  DL_Timer_setCaptCompUpdateMethod     
000040a1  DL_Timer_setClockConfig              
0000432d  DL_Timer_setCaptureCompareOutCtl     
000043cd  __TI_zero_init_nomemset              
00004471  __aeabi_uldivmod                     
00004499  TI_memcpy_small                      
000044ab  __TI_decompress_none                 
000044cd  DL_Timer_setCaptureCompareValue      
000044dd  SYSCFG_DL_SYSTICK_init               
000044ed  wcslen                               
000044fd  __aeabi_memset                       
000044fd  __aeabi_memset4                      
000044fd  __aeabi_memset8                      
00004519  TI_memset_small                      
00004529  Sys_GetTick                          
00004535  DL_Common_delayCycles                
0000456d  SysTick_Handler                      
00004575  __aeabi_errno_addr                   
0000457d  __aeabi_memcpy                       
0000457d  __aeabi_memcpy4                      
0000457d  __aeabi_memcpy8                      
00004585  abort                                
0000458b  ADC0_IRQHandler                      
0000458b  ADC1_IRQHandler                      
0000458b  AES_IRQHandler                       
0000458b  CANFD0_IRQHandler                    
0000458b  DAC0_IRQHandler                      
0000458b  DMA_IRQHandler                       
0000458b  Default_Handler                      
0000458b  GROUP0_IRQHandler                    
0000458b  HardFault_Handler                    
0000458b  I2C0_IRQHandler                      
0000458b  I2C1_IRQHandler                      
0000458b  NMI_Handler                          
0000458b  PendSV_Handler                       
0000458b  RTC_IRQHandler                       
0000458b  SPI0_IRQHandler                      
0000458b  SPI1_IRQHandler                      
0000458b  SVC_Handler                          
0000458b  TIMA0_IRQHandler                     
0000458b  TIMA1_IRQHandler                     
0000458b  TIMG0_IRQHandler                     
0000458b  TIMG12_IRQHandler                    
0000458b  TIMG6_IRQHandler                     
0000458b  TIMG7_IRQHandler                     
0000458b  TIMG8_IRQHandler                     
0000458b  UART0_IRQHandler                     
0000458b  UART1_IRQHandler                     
0000458b  UART2_IRQHandler                     
0000458b  UART3_IRQHandler                     
0000458e  C$$EXIT                              
0000458f  HOSTexit                             
00004593  Reset_Handler                        
00004597  _system_pre_init                     
000045a0  asc2_1608                            
00004b90  asc2_0806                            
00004dc0  __aeabi_ctype_table_                 
00004dc0  __aeabi_ctype_table_C                
00004ec1  _IQ6div_lookup                       
00004fd4  __TI_Handler_Table_Base              
00004fe0  __TI_Handler_Table_Limit             
00004fe8  __TI_CINIT_Base                      
00004ff8  __TI_CINIT_Limit                     
00004ff8  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  tick                                 
20200638  ExISR_Flag                           
2020063c  Motor_Font_Left                      
20200684  Motor_Font_Right                     
202006cc  Motor                                
202006d4  Data_Tracker_Input                   
202006db  Motor_Flag                           
202006dc  Data_MotorEncoder                    
202006e0  Data_Motor_TarSpeed                  
202006e4  Data_Tracker_Offset                  
202006e8  __aeabi_errno                        
202006ec  delayTick                            
202006f0  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[230 symbols]
